﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using MOCD_CommonAPI.Constant;
using MOCD_CommonAPI.ExternalWrapperServices.EWEAPI;
using MOCD_CommonAPI.Master.Responses;
using MOCD_CommonAPI.Mohesr;
using MOCD_CommonAPI.Refund;
using MOCD_CommonAPI.Request;
using MOCD_CRMWrapperServices.Assets;
using MOCD_CRMWrapperServices.Constants;
using MOCD_CRMWrapperServices.Helper;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.IdentityModel.Metadata;
using System.Linq;
using System.Threading.Tasks;
using Contact = MOCD_CRMWrapperServices.Assets.Contact;

namespace MOCD_CRMWrapperServices.Services
{
    public class RequestService
    {
        //private static IOrganizationService _gblService { get; set; }

        public static Guid CreateRequest(IOrganizationService service, Request _request)
        {
            Logger.Info("Inside CreateRequest method");
            Logger.Info("CreateRequest Body: " + JsonConvert.SerializeObject(_request));

            try
            {

                var request = new hexa_Request();
                request.hexa_PortalContact = new EntityReference(Contact.EntityLogicalName, _request.IdContact);
                request.hexa_ProcessTemplate = new EntityReference(hexa_ProcessTemplate.EntityLogicalName, _request.IdProcessTemplate);
                request.mocd_AllowanceCategory = new EntityReference(mocd_allowancecategory.EntityLogicalName, _request.IdAllowanceCategory);
                var response = service.Create(request);

                Logger.Info("CreateRequest Response: " + JsonConvert.SerializeObject(response));

                return response;

            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from CreateRequest: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static bool UpdateRequest(IOrganizationService service, Request _request)
        {
            Logger.Info("Inside UpdateRequest method");
            Logger.Info("UpdateRequest Body: " + JsonConvert.SerializeObject(_request));

            try
            {
                var request = new hexa_Request();
                request.Id = _request.IdRequest;
                request.hexa_PortalContact = new EntityReference(Contact.EntityLogicalName, _request.IdContact);
                request.hexa_ProcessTemplate = new EntityReference(hexa_ProcessTemplate.EntityLogicalName, _request.IdProcessTemplate);
                request.mocd_AllowanceCategory = new EntityReference(mocd_allowancecategory.EntityLogicalName, _request.IdAllowanceCategory);
                service.Update(request);

                Logger.Info("After updating the request in UpdateRequest method");

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from UpdateRequest: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
        public static RequestResponse RetrieveRequest(IOrganizationService service, string _requestId)
        {
            Logger.Info("Inside RetrieveRequest method");
            Logger.Info("RetrieveRequest _requestId: " + JsonConvert.SerializeObject(_requestId));

            try
            {
                string fetchxml = @"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true'>
	                                <entity name='hexa_request'>
		                                <attribute name='hexa_name'/>
		                                <attribute name='hexa_processtemplate'/>
		                                <attribute name='hexa_portalcontact'/>
		                                <attribute name='hexa_requestid'/>
		                                <order attribute='createdon' descending='true'/>
		                                <filter type='and'>
			                                <condition attribute='hexa_requestid' operator='eq' value='" + _requestId + @"'/>
		                                </filter>
		                                <link-entity name='contact' alias='con' link-type='outer' from='contactid' to='hexa_portalcontact'>
			                                <attribute name='contactid'/>
			                                <attribute name='firstname'/>
			                                <attribute name='lastname'/>
		                                </link-entity>
		                                <link-entity name='hexa_processtemplate' alias='temp' link-type='outer' from='hexa_processtemplateid' to='hexa_processtemplate'>
			                                <attribute name='hexa_processtemplateid'/>
			                                <attribute name='hexa_name'/>
		                                </link-entity>
	                                </entity>
                                </fetch>";

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();

                    PortalContact _contact = new PortalContact()
                    {
                        ContactId = entity.Contains("con.contactid") ? entity.GetAttributeValue<AliasedValue>("con.contactid").Value.ToString() : String.Empty,
                        FirstName = entity.Contains("con.firstname") ? entity.GetAttributeValue<AliasedValue>("con.firstname").Value.ToString() : String.Empty,
                        LastName = entity.Contains("con.lastname") ? entity.GetAttributeValue<AliasedValue>("con.lastname").Value.ToString() : String.Empty,
                    };

                    ProcessTemplate _template = new ProcessTemplate()
                    {
                        TemplateId = entity.Contains("temp.hexa_processtemplateid") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_processtemplateid").Value.ToString() : String.Empty,
                        TemplateName = entity.Contains("temp.hexa_name") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_name").Value.ToString() : String.Empty,
                    };

                    RequestResponse _obj = new RequestResponse()
                    {
                        RequestName = entity.Contains("hexa_name") ? entity.GetAttributeValue<string>("hexa_name") : String.Empty,
                        Contact = _contact,
                        Template = _template
                    };

                    Logger.Info("RetrieveRequest Response: " + JsonConvert.SerializeObject(_obj));

                    return _obj;
                }
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from RetrieveRequest: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<RequestResponse> RetrieveAllRequests(IOrganizationService service, string _emirateId)
        {
            Logger.Info("Inside RetrieveAllRequest method");
            Logger.Info("RetrieveAllRequest _emirateId: " + JsonConvert.SerializeObject(_emirateId));

            try
            {
                List<RequestResponse> _obj = new List<RequestResponse>();

                string fetchxml = @"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true'>
	                                <entity name='hexa_request'>
		                                <attribute name='hexa_name'/>
		                                <attribute name='hexa_processtemplate'/>
		                                <attribute name='hexa_portalcontact'/>
		                                <attribute name='hexa_requestid'/>
		                                <attribute name='hexa_firstname'/>
		                                <attribute name='hexa_lastname'/>
		                                <attribute name='emailaddress'/>
		                                <attribute name='mocd_emiratecode'/>
		                                <attribute name='hexa_mobilenumber'/>
		                                <attribute name='mocd_alternatephone'/>
		                                <attribute name='createdon'/>
                                        <attribute name='modifiedon' />
                                        <attribute name='mocd_inflationtype' />
                                        <attribute name='hexa_parentrequest' />
		                                <attribute name='hexa_internalstatus'/>
		                                <attribute name='pif_title'/>
		                                <order attribute='createdon' descending='true'/>
                                       
		                                <link-entity name='contact' alias='con' link-type='inner' from='contactid' to='hexa_portalcontact'>
			                                <attribute name='contactid'/>
			                                <attribute name='firstname'/>
			                                <attribute name='lastname'/>
			                                <attribute name='mocd_fullnamearabic'/>
			                                <attribute name='mocd_firstnamearabic'/>
			                                <attribute name='mocd_lastnamearabic'/>
			                                <attribute name='mocd_emiratesid'/>
                                            <attribute name='emailaddress1' />
                                            <attribute name='mobilephone' />
                                            <filter type='and'>
                                              <condition attribute='mocd_emiratesid' operator='eq' value='" + _emirateId + @"' />
                                            </filter>
		                                </link-entity>
		                                <link-entity name='hexa_processtemplate' alias='temp' link-type='outer' from='hexa_processtemplateid' to='hexa_processtemplate'>
			                                <attribute name='hexa_processtemplateid'/>
			                                <attribute name='hexa_name'/>
			                                <attribute name='mocd_arabicdisplayname'/>
			                                <attribute name='hexa_portaldisplayname'/>
		                                </link-entity>
                                        <link-entity name='hexa_processstatustemplate' from='hexa_processstatustemplateid' to='hexa_externalstatus' link-type='outer' alias='status'>
                                          <attribute name='hexa_processstatustemplateid' />      
                                          <attribute name='hexa_name' />      
                                        </link-entity>
	                                </entity>
                                </fetch>";

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (var entity in records.Entities)
                    {
                        //Guid idProcessTemplate = (Guid)((AliasedValue)entity["temp.hexa_processtemplate"]).Value;
                        Guid idTemp = entity.GetAttributeValue<EntityReference>("hexa_processtemplate").Id;
                        if (entity.GetAttributeValue<EntityReference>("hexa_processtemplate").Id == Guid.Parse("94b2a9e5-d2ae-ee11-a568-000d3a6c23a9") && entity.GetAttributeValue<string>("pif_title") != "Inflation - Portal")
                        {
                            continue;
                        }

                        #region Handle Child cases

                        if (entity.Contains(hexa_Request.Fields.hexa_ParentRequest) && entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ParentRequest).Id != null && entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ParentRequest).Id != Guid.Empty)
                        {
                            KeyValuePair<string, string> pendingwithCustomerInternalStatus = HexaConfiguration.FetchDataFromConfiguration("CaseInternalStatus-PendingwithCustomer", service);
                            KeyValuePair<string, string> eligibleApprovedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);
                            KeyValuePair<string, string> inelegibileRejectedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("InelegibileRejected_InternalStatus", service);
                            KeyValuePair<string, string> submittedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("IdRequestStatus_Submit", service);
                            KeyValuePair<string, string> draftedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("case_drafted_process_status_template", service);
                            KeyValuePair<string, string> eligableApprovedRefunded = HexaConfiguration.FetchDataFromConfiguration("EligableApprovedRefunded_InternalStatus", service);
                            KeyValuePair<string, string> notEligableApprovedRefunded = HexaConfiguration.FetchDataFromConfiguration("NotEligableRefunded_InternalStatus", service);
                            KeyValuePair<string, string> eligableApprovedPendingRefundInternalStatus = HexaConfiguration.FetchDataFromConfiguration("EligableApprovedPendingRefund_InternalStatus", service);
                            KeyValuePair<string, string> notEligablePendingRefundInternalStatus = HexaConfiguration.FetchDataFromConfiguration("NotEligablePendingRefund_InternalStatus", service);
                            KeyValuePair<string, string> RefundedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("Refunded_InternalStatus", service);
                            KeyValuePair<string, string> awaitingProviderStatusFeedBack = HexaConfiguration.FetchDataFromConfiguration("caseStatus_AwaitingProviderFeedbackAndAudit", service);

                            Guid parentCaseId = entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ParentRequest).Id;
                            Guid childInternalStatus = entity.Contains(hexa_Request.Fields.hexa_InternalStatus) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;


                            string parentCaseFetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='hexa_request'>
    <attribute name='hexa_name' />
    <attribute name='createdon' />
    <attribute name='hexa_requestid' />
    <attribute name='hexa_portalcontact' />
    <attribute name='ownerid' />
    <attribute name='hexa_internalstatus' />
    <attribute name='hexa_externalstatus' />
    <attribute name='mocd_mobilephone' />
    <attribute name='mocd_emiratesid' />
    <attribute name='emailaddress' />
    <order attribute='createdon' descending='true' />
    <order attribute='hexa_name' descending='true' />
    <filter type='and'>
      <condition attribute='hexa_requestid' operator='eq' uiname='154210' uitype='hexa_request' value='" + parentCaseId + @"' />
    </filter>
  </entity>
</fetch>";

                            Entity parentRecord = service.RetrieveMultiple(new FetchExpression(parentCaseFetchxml)).Entities.FirstOrDefault();
                            Guid parentinternalStatus = parentRecord.Contains(hexa_Request.Fields.hexa_InternalStatus) ? parentRecord.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;

                            if (!(childInternalStatus.ToString().ToLower() == eligibleApprovedInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == submittedInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == inelegibileRejectedInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == pendingwithCustomerInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == eligableApprovedPendingRefundInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == notEligablePendingRefundInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == RefundedInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() == eligableApprovedRefunded.Value.ToLower() || childInternalStatus.ToString().ToLower() == notEligableApprovedRefunded.Value.ToLower() || childInternalStatus.ToString().ToLower() == awaitingProviderStatusFeedBack.Value.ToLower()))
                            {
                                if (parentinternalStatus.ToString().ToLower() == eligibleApprovedInternalStatus.Value.ToLower())
                                {
                                    //childInternalStatus.ToString().ToLower() == submittedInternalStatus.Value.ToLower() || 
                                    if (!(childInternalStatus.ToString().ToLower() == draftedInternalStatus.Value.ToLower()))
                                    {
                                        continue;
                                    }
                                }
                                else
                                {
                                    continue;
                                }
                            }

                        }

                        #endregion

                        PortalContact _contact = new PortalContact()
                        {
                            ContactId = entity.Contains("con.contactid") ? entity.GetAttributeValue<AliasedValue>("con.contactid").Value.ToString() : String.Empty,
                            FirstName = entity.Contains("con.firstname") ? entity.GetAttributeValue<AliasedValue>("con.firstname").Value.ToString() : String.Empty,
                            FirstNameAr = entity.Contains("con.mocd_firstnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_firstnamearabic").Value.ToString() : String.Empty,
                            LastName = entity.Contains("con.lastname") ? entity.GetAttributeValue<AliasedValue>("con.lastname").Value.ToString() : String.Empty,
                            LastNameAr = entity.Contains("con.mocd_lastnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_lastnamearabic").Value.ToString() : String.Empty,
                            FullNameAr = entity.Contains("con.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_fullnamearabic").Value.ToString() : String.Empty,
                            FullNameEn = entity.Contains("con.lastname") ? entity.GetAttributeValue<AliasedValue>("con.lastname").Value.ToString() : String.Empty,
                            EmirateID = entity.Contains("con.mocd_emiratesid") ? entity.GetAttributeValue<AliasedValue>("con.mocd_emiratesid").Value.ToString() : String.Empty,
                            //Email = entity.Contains("con.emailaddress1") ? entity.GetAttributeValue<AliasedValue>("con.emailaddress1").Value.ToString() : String.Empty,
                            Email = entity.Contains("emailaddress") ? entity.GetAttributeValue<string>("emailaddress") : string.Empty,
                            //MobileNumber = entity.Contains("con.mobilephone") ? entity.GetAttributeValue<AliasedValue>("con.mobilephone").Value.ToString() : String.Empty
                            MobileNumber = entity.Contains("mocd_alternatephone") ? entity.GetAttributeValue<string>("mocd_alternatephone").ToString() : string.Empty
                        };

                        string _fullname = entity.Contains("con.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_fullnamearabic").Value.ToString() : String.Empty;
                        string[] name = _fullname.Split(' ');

                        if (name != null && name.Length > 0)
                        {
                            if (!string.IsNullOrWhiteSpace(_fullname) && string.IsNullOrEmpty(_contact.LastNameAr))
                                _contact.LastNameAr = name.LastOrDefault();
                            if (!string.IsNullOrWhiteSpace(_fullname) && string.IsNullOrEmpty(_contact.FirstNameAr))
                                _contact.FirstNameAr = name.FirstOrDefault();
                        }

                        ProcessTemplate _template = new ProcessTemplate()
                        {
                            TemplateId = entity.Contains("temp.hexa_processtemplateid") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_processtemplateid").Value.ToString() : string.Empty,
                            TemplateName = entity.Contains("temp.hexa_portaldisplayname") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_portaldisplayname").Value.ToString() : string.Empty,
                            TemplateNameAr = entity.Contains("temp.mocd_arabicdisplayname") ? entity.GetAttributeValue<AliasedValue>("temp.mocd_arabicdisplayname").Value.ToString() : string.Empty
                        };

                        StatusDetails _status = new StatusDetails()
                        {
                            Key = entity.Contains("status.hexa_processstatustemplateid") ? entity.GetAttributeValue<AliasedValue>("status.hexa_processstatustemplateid").Value.ToString() : String.Empty,
                            Value = entity.Contains("status.hexa_name") ? entity.GetAttributeValue<AliasedValue>("status.hexa_name").Value.ToString() : String.Empty
                        };

                        RequestResponse _details = new RequestResponse()
                        {
                            RequestName = entity.Contains("hexa_name") ? entity.GetAttributeValue<string>("hexa_name") : String.Empty,
                            Contact = _contact,
                            Template = _template,
                            Status = _status,
                            CreatedDate = entity.Contains("createdon") ? entity.GetAttributeValue<DateTime>("createdon") : DateTime.MinValue,
                            CaseId = entity.Id,
                            EmirateId = entity.Contains(hexa_Request.Fields.mocd_emiratecode) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_emiratecode).Id : Guid.Empty
                        };
                        if (entity.Contains("mocd_inflationtype"))
                        {
                            int inflationType = entity.Contains("mocd_inflationtype") ? entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_InflationType).Value : -1;
                            if (inflationType == (int)hexa_Request_mocd_InflationType.NominatedbySocialSupportEntity || inflationType == (int)hexa_Request_mocd_InflationType.BaseAllowanceInflation)
                            {
                                _details.IsNomiatedInflationCase = true;
                            }
                        }
                        #region Check case if eligible for appeal or edit
                        DateTime modifiedon = entity.Contains("modifiedon") ? entity.GetAttributeValue<DateTime>("modifiedon") : DateTime.MinValue;
                        TimeSpan timeSpan = DateTime.Now.ToLocalTime() - modifiedon;
                        int numberOfDays = timeSpan.Days;

                        KeyValuePair<string, string> eligibleApprovedExternalStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_ExternalStatus", service);
                        KeyValuePair<string, string> notEligibleRejectedExternalStatus = HexaConfiguration.FetchDataFromConfiguration("NotEligibleRejected_ExternalStatus", service);
                        KeyValuePair<string, string> eligibleApprovedStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);

                        string caseStatusFetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                        <entity name='hexa_request'>
                                                          <attribute name='hexa_name' />
                                                          <attribute name='createdon' />
                                                          <attribute name='hexa_requestid' />
                                                          <order attribute='createdon' descending='true' />
                                                          <order attribute='hexa_name' descending='true' />
                                            <filter type='and'>
                                              <condition attribute='hexa_externalstatus' operator='in'>
                                                <value uiname='Eligible / Approved' uitype='hexa_processstatustemplate'>{" + eligibleApprovedExternalStatus.Value + @"}</value>
                                                <value uiname='Not Eligible / Rejected' uitype='hexa_processstatustemplate'>{" + notEligibleRejectedExternalStatus.Value + @"}</value>
                                              </condition>
                                              <condition attribute='hexa_requestid' operator='eq' uiname='74235' uitype='hexa_request' value='" + entity.Id + @"' />
                                            </filter>
                                                        </entity>
                                                      </fetch>";

                        string appealFetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                    <entity name='mocd_appeal'>
                                                      <attribute name='mocd_appealid' />
                                                      <attribute name='mocd_name' />
                                                      <attribute name='createdon' />
                                                      <order attribute='mocd_name' descending='false' />
                                                      <filter type='and'>
                                                        <condition attribute='mocd_case' operator='eq' uiname='152238' uitype='hexa_request' value='" + entity.Id + @"' />
                                                      </filter>
                                                    </entity>
                                                  </fetch>";

                        string EditFetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                  <entity name='hexa_request'>
                                                    <attribute name='hexa_name' />
                                                    <attribute name='hexa_requestid' />
                                                    <attribute name='hexa_portalcontact' />
                                                    <attribute name='mocd_emiratesid' />
                                                    <order attribute='hexa_name' descending='true' />
                                                    <filter type='and'>
                                                      <condition attribute='hexa_internalstatus' operator='eq' uiname='Eligible / Approved' uitype='hexa_processstatustemplate' value='{" + eligibleApprovedStatus.Value + @"}' />
                                                      <condition attribute='hexa_requestid' operator='eq' uiname='74235' uitype='hexa_request' value='" + entity.Id + @"' />
                                                    </filter>
                                                  </entity>
                                                </fetch>";

                        EntityCollection appealRecords = service.RetrieveMultiple(new FetchExpression(appealFetchxml));
                        EntityCollection caseStatusRecords = service.RetrieveMultiple(new FetchExpression(caseStatusFetchxml));
                        EntityCollection editRecords = service.RetrieveMultiple(new FetchExpression(EditFetchxml));

                        _details.EligibleForAppeal = false;
                        _details.EligibleForEdit = false;

                        if ((!(appealRecords.Entities != null && appealRecords.Entities.Count > 0)) && numberOfDays <= 30 && (caseStatusRecords.Entities != null && caseStatusRecords.Entities.Count > 0))
                        {
                            _details.EligibleForAppeal = true;
                        }

                        if (editRecords.Entities != null && editRecords.Entities.Count > 0)
                        {
                            _details.EligibleForEdit = true;

                            KeyValuePair<string, string> eligibleApprovedIntStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);
                            KeyValuePair<string, string> inelegibileRejectedIntStatus = HexaConfiguration.FetchDataFromConfiguration("InelegibileRejected_InternalStatus", service);

                            string fetchXml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                  <entity name='hexa_request'>
                                                    <attribute name='hexa_name' />
                                                    <attribute name='createdon' />
                                                    <attribute name='hexa_requestid' />
                                                    <attribute name='hexa_portalcontact' />
                                                    <attribute name='ownerid' />
                                                    <attribute name='hexa_internalstatus' />
                                                    <attribute name='hexa_externalstatus' />
                                                    <attribute name='mocd_mobilephone' />
                                                    <attribute name='mocd_emiratesid' />
                                                    <attribute name='emailaddress' />
                                                    <order attribute='createdon' descending='true' />
                                                    <order attribute='hexa_name' descending='true' />
                                                    <filter type='and'>
                                                      <condition attribute='hexa_parentrequest' operator='eq' uiname='77092' uitype='hexa_request' value='" + entity.Id + @"' />
                                                      <condition attribute='hexa_internalstatus' operator='not-in'>
                                                        <value uiname='Eligible / Approved' uitype='hexa_processstatustemplate'>{" + eligibleApprovedIntStatus.Value + @"}</value>
                                                        <value uiname='Not Eligible / Rejected' uitype='hexa_processstatustemplate'>{" + inelegibileRejectedIntStatus.Value + @"}</value>
                                                      </condition>
                                                    </filter>
                                                  </entity>
                                                </fetch>";

                            EntityCollection ChildRecords = service.RetrieveMultiple(new FetchExpression(fetchXml));

                            if (ChildRecords.Entities != null && ChildRecords.Entities.Count > 0)
                            {
                                _details.EligibleForEdit = false;
                            }
                        }
                        #endregion

                        _obj.Add(_details);
                    }

                    Logger.Info("RetrieveAllRequests Response: " + JsonConvert.SerializeObject(_obj));

                    return _obj;
                }
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from RetrieveAllRequests: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<RequestResponse> RetrieveRequests(IOrganizationService service)
        {
            Logger.Info("Inside RetrieveAllRequest method");

            try
            {
                string fetchxml = @"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true'>
	                                <entity name='hexa_request'>
		                                <attribute name='hexa_name'/>
		                                <attribute name='hexa_processtemplate'/>
		                                <attribute name='hexa_portalcontact'/>
		                                <attribute name='hexa_internalstatus'/>
		                                <attribute name='hexa_externalstatus'/>
		                                <attribute name='hexa_requestid'/>
		                                <order attribute='createdon' descending='true'/>
		                                <filter type='and'>
			                                <condition attribute='statecode' operator='eq' value='0'/>
		                                </filter>
		                                <link-entity name='contact' alias='con' link-type='outer' from='contactid' to='hexa_portalcontact'>
			                              <attribute name='contactid'/>
			                              <attribute name='firstname'/>
			                              <attribute name='lastname'/>
			                              <attribute name='mocd_firstnamearabic'/>
			                              <attribute name='mocd_lastnamearabic'/>
			                              <attribute name='mocd_emiratesid'/>
                                          <attribute name='emailaddress1' />
                                          <attribute name='mobilephone' />    
		                                </link-entity>
		                                <link-entity name='hexa_processtemplate' alias='temp' link-type='outer' from='hexa_processtemplateid' to='hexa_processtemplate'>
			                                <attribute name='hexa_processtemplateid'/>
			                                <attribute name='hexa_name'/>
		                                </link-entity>
	                                </entity>
                                </fetch>";

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    List<RequestResponse> _requests = new List<RequestResponse>();

                    foreach (var entity in records.Entities)
                    {
                        PortalContact _contact = new PortalContact()
                        {
                            ContactId = entity.Contains("con.contactid") ? entity.GetAttributeValue<AliasedValue>("con.contactid").Value.ToString() : string.Empty,
                            FirstName = entity.Contains("con.firstname") ? entity.GetAttributeValue<AliasedValue>("con.firstname").Value.ToString() : string.Empty,
                            LastName = entity.Contains("con.lastname") ? entity.GetAttributeValue<AliasedValue>("con.lastname").Value.ToString() : string.Empty,
                            FirstNameAr = entity.Contains("con.mocd_firstnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_firstnamearabic").Value.ToString() : string.Empty,
                            LastNameAr = entity.Contains("con.mocd_lastnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_lastnamearabic").Value.ToString() : string.Empty,
                            EmirateID = entity.Contains("con.mocd_emiratesid") ? entity.GetAttributeValue<AliasedValue>("con.mocd_emiratesid").Value.ToString() : string.Empty,
                            Email = entity.Contains("con.emailaddress1") ? entity.GetAttributeValue<AliasedValue>("con.emailaddress1").Value.ToString() : string.Empty,
                            MobileNumber = entity.Contains("con.mobilephone") ? entity.GetAttributeValue<AliasedValue>("con.mobilephone").Value.ToString() : string.Empty
                        };

                        ProcessTemplate _template = new ProcessTemplate()
                        {
                            TemplateId = entity.Contains("temp.hexa_processtemplateid") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_processtemplateid").Value.ToString() : String.Empty,
                            TemplateName = entity.Contains("temp.hexa_name") ? entity.GetAttributeValue<AliasedValue>("temp.hexa_name").Value.ToString() : String.Empty,
                        };

                        RequestResponse _obj = new RequestResponse()
                        {
                            RequestName = entity.Contains("hexa_name") ? entity.GetAttributeValue<string>("hexa_name") : string.Empty,
                            Contact = _contact,
                            Template = _template
                        };
                        _requests.Add(_obj);
                    }

                    Logger.Info("RetrieveAllRequests Response: " + JsonConvert.SerializeObject(_requests));

                    return _requests;
                }
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from RetrieveRequests: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<AllowanceTransation> RetrieveAllowanceTransactions(IOrganizationService service, string _emirateId)
        {
            Logger.Info("Inside RetrieveAllowanceTransactions method");
            Logger.Info("RetrieveAllowanceTransactions _emirateId: " + JsonConvert.SerializeObject(_emirateId));

            try
            {
                List<AllowanceTransation> _obj = new List<AllowanceTransation>();

                string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='mocd_allowancetransaction'>
                                    <attribute name='mocd_allowancetransactionid' />
                                    <attribute name='mocd_name' />
                                    <attribute name='mocd_payoutdate' />
                                    <attribute name='mocd_totalallowance' />
                                    <order attribute='mocd_name' descending='false' />
                                    <link-entity name='contact' from='contactid' to='mocd_beneficiary' link-type='inner' alias='con'>
			                          <attribute name='contactid'/>
			                          <attribute name='firstname'/>
			                          <attribute name='lastname'/>
			                          <attribute name='mocd_firstnamearabic'/>
			                          <attribute name='mocd_lastnamearabic'/>
			                          <attribute name='mocd_emiratesid'/>
                                      <attribute name='emailaddress1' />
                                      <attribute name='mobilephone' />                                      
                                      <filter type='and'>
                                        <condition attribute='mocd_emiratesid' operator='eq' value='" + _emirateId + @"' />
                                      </filter>
                                    </link-entity>
                                    <link-entity name='hexa_request' from='hexa_requestid' to='mocd_request' link-type='inner' alias='req'>
                                      <attribute name='hexa_name' /> 
                                      <filter type='and'>
                                        <condition attribute='statuscode' operator='eq' value='110000001' />
                                      </filter>
                                    </link-entity>
                                  </entity>
                                </fetch>";

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    AllowanceTransation _details = new AllowanceTransation();

                    foreach (var entity in records.Entities)
                    {
                        PortalContact _contact = new PortalContact()
                        {
                            ContactId = entity.Contains("con.contactid") ? entity.GetAttributeValue<AliasedValue>("con.contactid").Value.ToString() : string.Empty,
                            FirstName = entity.Contains("con.firstname") ? entity.GetAttributeValue<AliasedValue>("con.firstname").Value.ToString() : string.Empty,
                            LastName = entity.Contains("con.lastname") ? entity.GetAttributeValue<AliasedValue>("con.lastname").Value.ToString() : string.Empty,
                            FirstNameAr = entity.Contains("con.mocd_firstnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_firstnamearabic").Value.ToString() : string.Empty,
                            LastNameAr = entity.Contains("con.mocd_lastnamearabic") ? entity.GetAttributeValue<AliasedValue>("con.mocd_lastnamearabic").Value.ToString() : string.Empty,
                            EmirateID = entity.Contains("con.mocd_emiratesid") ? entity.GetAttributeValue<AliasedValue>("con.mocd_emiratesid").Value.ToString() : string.Empty,
                            Email = entity.Contains("con.emailaddress1") ? entity.GetAttributeValue<AliasedValue>("con.emailaddress1").Value.ToString() : string.Empty,
                            MobileNumber = entity.Contains("con.mobilephone") ? entity.GetAttributeValue<AliasedValue>("con.mobilephone").Value.ToString() : string.Empty
                        };

                        _details = new AllowanceTransation()
                        {
                            Name = entity.Contains("mocd_name") ? entity.GetAttributeValue<string>("mocd_name") : string.Empty,
                            TotalAmount = entity.Contains("mocd_totalallowance") ? entity.GetAttributeValue<string>("mocd_totalallowance") : string.Empty,
                            PayDate = entity.Contains("mocd_payoutdate") ? entity.GetAttributeValue<DateTime>("mocd_payoutdate").ToUniversalTime() : DateTime.MinValue,
                            Contact = _contact,
                            RequestName = entity.Contains("req.hexa_name") ? entity.GetAttributeValue<AliasedValue>("req.hexa_name").Value.ToString() : string.Empty,
                        };

                        _obj.Add(_details);
                    }

                    Logger.Info("RetrieveAllowanceTransactions Response: " + JsonConvert.SerializeObject(_obj));

                    return _obj;
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from RetrieveAllowanceTransactions: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static ResponseCaseById GetCaseRequestByCaseId(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseRequestByCaseId method");
            Logger.Info("GetCaseRequestByCaseId " + JsonConvert.SerializeObject("Case Id: " + caseId + " beneficiary Id: " + beneficiaryId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);
                if (beneficiaryId == Guid.Empty)
                    throw new Exception(CustomMessages.BeneficiaryId);
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseRequestByIdXML(caseId, beneficiaryId)));
                ResponseCaseById response = new ResponseCaseById();
                Case caseResponse = new Case();

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        caseResponse.AccomodationType = entity.Contains(hexa_Request.Fields.mocd_AccommodationType) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_AccommodationType).Id : Guid.Empty;
                        caseResponse.Occupation = entity.Contains(hexa_Request.Fields.mocd_Occupation) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_Occupation).Id : Guid.Empty;
                        caseResponse.Education = entity.Contains(hexa_Request.Fields.mocd_Education) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_Education).Id : Guid.Empty;
                        caseResponse.Emirate = entity.Contains(hexa_Request.Fields.mocd_emiratecode) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_emiratecode).Id : Guid.Empty;
                        caseResponse.JobTitle = entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_JobTitle);
                        caseResponse.Alternativenumber = entity.Contains(hexa_Request.Fields.mocd_AlternatePhone) ? entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_AlternatePhone) : string.Empty;
                        caseResponse.AlternativeEmail = entity.Contains(hexa_Request.Fields.EmailAddress) ? entity.GetAttributeValue<string>(hexa_Request.Fields.EmailAddress) : string.Empty;
                        caseResponse.IsHouseholdHeadContributeToIncome = entity.Contains(hexa_Request.Fields.mocd_householdcontributestoincome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdcontributestoincome) : false;
                        caseResponse.IshouseholdHeadTradeLicense = entity.Contains(hexa_Request.Fields.mocd_householdtradelicense) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdtradelicense) : false;
                        caseResponse.CaseRef = entity.Contains(hexa_Request.Fields.hexa_Name) ? entity.GetAttributeValue<string>(hexa_Request.Fields.hexa_Name) : string.Empty;
                        caseResponse.Address = entity.Contains(hexa_Request.Fields.mocd_Address) ? entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_Address) : string.Empty;
                        caseResponse.IsHouseholdHeadReceivePensionIncome = entity.Contains(hexa_Request.Fields.mocd_householdretirementorpensionincome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdretirementorpensionincome) : false;
                        caseResponse.IshouseholdHeadReceiveRentalIncome = entity.Contains(hexa_Request.Fields.mocd_DoesthehouseholdheadreceivearentalIncome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_DoesthehouseholdheadreceivearentalIncome) : false;
                        caseResponse.ProcessTemplate = entity.Contains(hexa_Request.Fields.hexa_ProcessTemplate) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ProcessTemplate).Id : Guid.Empty;
                        caseResponse.MaritalStatus = entity.Contains(hexa_Request.Fields.mocd_MaritalStatus) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_MaritalStatus).Id : Guid.Empty;
                        caseResponse.LivingSituation = entity.Contains(hexa_Request.Fields.mocd_AccommodationType) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_AccommodationType).Id : Guid.Empty;
                        caseResponse.ApplyEducationAllowance = entity.Contains(hexa_Request.Fields.mocd_ApplyforEducationExcellenceAllowance) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ApplyforEducationExcellenceAllowance) : false;
                        caseResponse.ApplyHousingAllowance = entity.Contains(hexa_Request.Fields.mocd_ApplyforaHousingAllowance) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ApplyforaHousingAllowance) : false;
                        caseResponse.IsHouseholOwnerResidentialProperty = entity.Contains(hexa_Request.Fields.mocd_SoleOwnerofaConstructedResidentialPropert) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_SoleOwnerofaConstructedResidentialPropert) : false;
                        caseResponse.IsUtilityBillIssuedForFullyOwnedProperty = entity.Contains(hexa_Request.Fields.mocd_isutilitybillissuedforfullyownedproperty) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_isutilitybillissuedforfullyownedproperty) : false;
                        caseResponse.ReceivingFederalLocalhousingsupport = entity.Contains(hexa_Request.Fields.mocd_receivinglocalhousingsupport) ? entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_receivinglocalhousingsupport).Value : 0;
                        caseResponse.ReceivingHousingAllowanceFromEmployer = entity.Contains(hexa_Request.Fields.mocd_ReceivingHousingAllowanceSchemefromEmploy) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ReceivingHousingAllowanceSchemefromEmploy) : false;
                        caseResponse.ReceivesHousingSupportFromHusband = entity.Contains(hexa_Request.Fields.mocd_ReceivingHousingSupportfromHusband) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ReceivingHousingSupportfromHusband) : false;
                        caseResponse.FullOwnershipResidentialProperty = entity.Contains(hexa_Request.Fields.mocd_HasFullOwnershipoftheResidentialProperty) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_HasFullOwnershipoftheResidentialProperty) : false;

                        caseResponse.ApplyInflationAllowance = entity.Contains(hexa_Request.Fields.mocd_ApplyforInflationAllowance) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ApplyforInflationAllowance) : false;
                        caseResponse.ApplyUtilityAllowance = entity.Contains(hexa_Request.Fields.mocd_applyforutilityallowance) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_applyforutilityallowance) : false;
                        caseResponse.UtilityProvider = entity.Contains(hexa_Request.Fields.mocd_UtilityProvider) ? entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_UtilityProvider).Value : 0;
                        caseResponse.UtilityAccountNumber = entity.Contains(hexa_Request.Fields.mocd_UtilityAccountNumber) ? entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_UtilityAccountNumber) : string.Empty;

                        caseResponse.PortalPersona = entity.Contains(hexa_Request.Fields.mocd_portalpersona) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_portalpersona).Id : Guid.Empty;
                        caseResponse.SubPersona = entity.Contains(hexa_Request.Fields.mocd_portalsubpersona) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_portalsubpersona).Id : Guid.Empty;
                        caseResponse.Category = entity.Contains(hexa_Request.Fields.mocd_category) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_category).Id : Guid.Empty;
                        caseResponse.SubCategory = entity.Contains(hexa_Request.Fields.mocd_subcategory) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_subcategory).Id : Guid.Empty;
                        caseResponse.Area = entity.Contains(hexa_Request.Fields.mocd_area) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_area).Id : Guid.Empty;
                        caseResponse.Center = entity.Contains(hexa_Request.Fields.mocd_Center) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_Center).Id : Guid.Empty;
                        caseResponse.InflationCategory = entity.Contains("mocd_inflationreasonforapplying") ? entity.GetAttributeValue<EntityReference>("mocd_inflationreasonforapplying").Id : Guid.Empty;


                        caseResponse.EntityReceivedFrom = entity.Contains("mocd_entityreceivedfrom") ? entity.GetAttributeValue<OptionSetValue>("mocd_entityreceivedfrom").Value : 0;
                        caseResponse.EWEBill = entity.Contains("mocd_ewebill") ? entity.GetAttributeValue<string>("mocd_ewebill") : string.Empty;
                        caseResponse.ReceiveSocialAid = entity.Contains("mocd_receivesocialaid") ? entity.GetAttributeValue<bool>("mocd_receivesocialaid") : false;
                        caseResponse.ReceiveInflationAllowance = entity.Contains("mocd_receiveinflationallowance") ? entity.GetAttributeValue<bool>("mocd_receiveinflationallowance") : false;
                        caseResponse.RegisteredWithEWE = entity.Contains("mocd_registeredwithewe") ? entity.GetAttributeValue<bool>("mocd_registeredwithewe") : false;
                        caseResponse.RelatedEmiratesID = entity.Contains("mocd_relatedemiratesid") ? entity.GetAttributeValue<string>("mocd_relatedemiratesid") : string.Empty;

                        caseResponse.PursuingHigherEducation = entity.Contains("mocd_pursuinghighereducation") ? entity.GetAttributeValue<bool>("mocd_pursuinghighereducation") : false;
                        caseResponse.DraftedinMilitaryService = entity.Contains("mocd_draftedinmilitaryservice") ? entity.GetAttributeValue<bool>("mocd_draftedinmilitaryservice") : false;
                        caseResponse.ReceivedLocalSupport = entity.Contains("mocd_receivinglocalsocialsupport") ? entity.GetAttributeValue<OptionSetValue>("mocd_receivinglocalsocialsupport").Value : 0;
                        caseResponse.GuardianEmiratesID = entity.Contains(hexa_Request.Fields.mocd_guardian) ? GetEmiratesIdByContactId(service, entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_guardian).Id) : string.Empty;
                        caseResponse.NumberOfChildren = entity.Contains("mocd_numberofchildren") ? entity.GetAttributeValue<int?>("mocd_numberofchildren") : 0;
                        caseResponse.NumberOfChildrenLessThan25 = entity.Contains("mocd_numberofpodchildrenlessthan25") ? entity.GetAttributeValue<int?>("mocd_numberofpodchildrenlessthan25") : 0;

                        caseResponse.LegacyCaseType = entity.Contains("mocd_legacycasetype") ? entity.GetAttributeValue<OptionSetValue>("mocd_legacycasetype").Value : 0;
                        caseResponse.CaseForPODChild = entity.Contains("mocd_isthecaseforpodchild") ? entity.GetAttributeValue<bool>("mocd_isthecaseforpodchild") : false;


                        #region InflationCategory
                        //string fetchXML = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                        //                      <entity name='mocd_inflationcategoriesmapping'>
                        //                        <attribute name='mocd_inflationcategoriesmappingid' />
                        //                        <attribute name='mocd_name' />
                        //                        <attribute name='mocd_namear' />
                        //                        <attribute name='mocd_portalsubcategory' />
                        //                        <attribute name='mocd_portalcategory' />
                        //                        <order attribute='mocd_name' descending='false' />
                        //                        <filter type='and'>
                        //                          <condition attribute='mocd_portalcategory' operator='eq' uiname='Woman in difficult situation' uitype='mocd_portalcategory' value='" + caseResponse.Category + @"' />
                        //                          <condition attribute='mocd_portalsubcategory' operator='eq' uiname='Divorced' uitype='mocd_subcategory' value='" + caseResponse.SubCategory + @"' />
                        //                        </filter>
                        //                      </entity>
                        //                    </fetch>";

                        //EntityCollection categoryRecords = service.RetrieveMultiple(new FetchExpression(fetchXML));

                        //if (categoryRecords.Entities != null && categoryRecords.Entities.Count > 0)
                        //{
                        //    Entity categoryEntity = categoryRecords.Entities.FirstOrDefault();
                        //    caseResponse.InflationCategory = categoryEntity.Contains(mocd_inflationcategoriesmapping.Fields.mocd_inflationcategoriesmappingId) ? categoryEntity.GetAttributeValue<Guid>(mocd_inflationcategoriesmapping.Fields.mocd_inflationcategoriesmappingId) : Guid.Empty;
                        //}
                        #endregion

                        string values = string.Empty;
                        if (entity.Contains("mocd_childeligibilityforwomenindifficulty"))
                        {
                            OptionSetValueCollection optionSetCollection = entity.GetAttributeValue<OptionSetValueCollection>("mocd_childeligibilityforwomenindifficulty");
                            foreach (OptionSetValue optionSetValue in optionSetCollection)
                            {
                                values += "," + optionSetValue.Value;
                            }
                            if (!string.IsNullOrEmpty(values))
                            {
                                values = values.TrimStart(',');
                            }
                        }
                        caseResponse.ChildEligibilityforWomeninDifficulty = values;

                        values = string.Empty;
                        if (entity.Contains("mocd_eligibilitychecksource"))
                        {
                            OptionSetValueCollection optionSetCollection = entity.GetAttributeValue<OptionSetValueCollection>("mocd_eligibilitychecksource");
                            foreach (OptionSetValue optionSetValue in optionSetCollection)
                            {
                                values += "," + optionSetValue.Value;
                            }
                            if (!string.IsNullOrEmpty(values))
                            {
                                values = values.TrimStart(',');
                            }
                        }
                        caseResponse.localSocialSupportEntities = values;

                        caseResponse.IsActiveStudent = entity.Contains(hexa_Request.Fields.mocd_isactivestudent) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_isactivestudent) : false;
                        caseResponse.Terminated = entity.Contains(hexa_Request.Fields.mocd_terminated) ? entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_terminated).Value : 0;
                        caseResponse.MilitaryServiceStatus = entity.Contains(hexa_Request.Fields.mocd_militaryservicestatus) ? entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_militaryservicestatus).Value : 0;

                        response.IdCase = entity.Id;
                        response.IdStatus = entity.Contains(hexa_Request.Fields.hexa_InternalStatus) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;
                        response.CaseType = entity.Contains(hexa_Request.Fields.mocd_casetype) ? (int)entity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_casetype).Value : 1;
                        response.SubmissionTime = entity.Contains(hexa_Request.Fields.hexa_SubmittedOn) ? entity.GetAttributeValue<DateTime>(hexa_Request.Fields.hexa_SubmittedOn).ToLocalTime().ToString("o", CultureInfo.InvariantCulture) : string.Empty;
                        response.NameEn = entity.Contains("aliasContact.fullname") ? entity.GetAttributeValue<AliasedValue>("aliasContact.fullname").Value.ToString() : string.Empty;
                        response.NameAr = entity.Contains("aliasContact.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("aliasContact.mocd_fullnamearabic").Value.ToString() : string.Empty;
                        response.Email = entity.Contains("aliasContact.emailaddress2") ? entity.GetAttributeValue<AliasedValue>("aliasContact.emailaddress2").Value.ToString() : string.Empty;
                        response.Mobile = entity.Contains("aliasContact.telephone2") ? entity.GetAttributeValue<AliasedValue>("aliasContact.telephone2").Value.ToString() : string.Empty;

                        #region Check Refund
                        response.TotalRefundAmount = entity.Contains(hexa_Request.Fields.mocd_pendingrefundamount) ? entity.GetAttributeValue<Money>(hexa_Request.Fields.mocd_pendingrefundamount).Value : default(decimal);
                        int? paymentReceived = (int?)mocd_refundpayment_StatusCode.PaymentCompleted_Inactive;
                        int? PaymentComleted = (int?)mocd_refundpayment_StatusCode.PaymentCompleted_Active;
                        int? pendingPayment = (int?)mocd_refundpayment_StatusCode.PendingPayment;
                        string xml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>
                                        <entity name='mocd_refundpaymentheader'>
                                            <attribute name='mocd_refundpaymentheaderid' />
                                            <attribute name='mocd_name' />
                                            <attribute name='mocd_financialstatus' />
                                            <order attribute='mocd_name' descending='false' />
                                            <filter type='and'>
                                             <condition attribute='mocd_case' operator='eq' uitype='hexa_request' value='" + caseId + @"' />
                                            </filter>
                                            <link-entity name='mocd_refundpayment' from='mocd_refundpaymentheader'
                                                to='mocd_refundpaymentheaderid' link-type='inner' alias='aa'>
                                                <filter type='and'>
                                                    <condition attribute='statuscode' operator='in'>
                                                        <value>" + paymentReceived.Value + @"</value>
                                                        <value>" + PaymentComleted.Value + @"</value>
                                                        <value>" + pendingPayment.Value + @"</value>
                                                    </condition>
                                                </filter>
                                            </link-entity>
                                        </entity>
                                    </fetch>";

                        List<Entity> refundPaymentRecords = service.RetrieveMultiple(new FetchExpression(xml)).Entities.ToList();
                        response.IsPendingRefund = refundPaymentRecords.Count > 0 ? false : true;
                        response.RefundFinancialStatus = refundPaymentRecords.Count > 0 ? refundPaymentRecords.First().GetAttributeValue<OptionSetValue>(mocd_refundpaymentheader.Fields.mocd_financialstatus)?.Value : null;
                        #endregion

                        #region Check Allowance Eligibility
                        response.eligibleEducation = true;
                        response.eligibleHousing = true;

                        response.ParentCaseId = entity.Contains(hexa_Request.Fields.hexa_ParentRequest) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ParentRequest).Id : Guid.Empty;
                        if (response.ParentCaseId != null && response.ParentCaseId != Guid.Empty)
                        {
                            string childfetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                        <entity name='hexa_request'>
                                          <attribute name='hexa_name' />
                                          <attribute name='createdon' />
                                          <attribute name='hexa_requestid' />
                                          <attribute name='mocd_applyforeducationexcellenceallowance' />
                                          <attribute name='mocd_applyforahousingallowance' />
                                          <attribute name='hexa_internalstatus' />
                                          <order attribute='createdon' descending='true' />
                                          <order attribute='hexa_name' descending='true' />
                                          <filter type='and'>
                                            <condition attribute='hexa_parentrequest' operator='eq' uiname='74235' uitype='hexa_request'   value='" + response.ParentCaseId + @"' />
                                          </filter>
                                        </entity>
                                      </fetch>";

                            Entity childRecord = service.RetrieveMultiple(new FetchExpression(childfetchxml)).Entities.FirstOrDefault();
                            if (childRecord != null)
                            {
                                response.eligibleHousing = childRecord.Contains(hexa_Request.Fields.mocd_ApplyforaHousingAllowance) ? !(childRecord.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ApplyforaHousingAllowance)) : true;

                                List<EducationCase> educationCaseDetails = null;
                                object educationalAllowance = GetCaseEducationalAllowance(response.ParentCaseId, service);
                                if (educationalAllowance != null)
                                {
                                    dynamic educationDetails = educationalAllowance;
                                    educationCaseDetails = educationDetails.EducationCaseDetails;
                                }

                                if (educationCaseDetails != null && educationCaseDetails.Count > 0)
                                {
                                    if (educationCaseDetails.Any(x => x.ApplyEducationAllowance == true))
                                    {
                                        response.eligibleEducation = false;
                                    }
                                    else
                                    {
                                        response.eligibleEducation = true;
                                    }

                                }
                            }
                        }

                        #endregion

                        #region Check Inflation Edit Case
                        string FetchInflationXml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                              <entity name='hexa_request'>
                                                <attribute name='hexa_name' />
                                                <attribute name='createdon' />
                                                <attribute name='hexa_portalcontact' />
                                                <attribute name='ownerid' />
                                                <attribute name='hexa_internalstatus' />
                                                <attribute name='hexa_externalstatus' />
                                                <attribute name='mocd_emiratesid' />
                                                <attribute name='hexa_requestid' />
                                                <attribute name='mocd_inflationtype' />
                                                <order attribute='createdon' descending='true' />
                                                <order attribute='hexa_name' descending='true' />
                                                <filter type='and'>
                                                  <condition attribute='hexa_requestid' operator='eq' uiname='000032' uitype='hexa_request' value='" + caseId + @"' />
                                                  <condition attribute='mocd_inflationtype' operator='in'>
                                                    <value>662410000</value>
                                                    <value>662410001</value>
                                                    <value>662410002</value>
                                                  </condition>
                                                  <condition attribute='hexa_parentrequest' operator='not-null' />
                                                </filter>
                                              </entity>
                                            </fetch>";

                        response.IsInflationBaseEdit = false;
                        response.IsInflationStandAloneEdit = false;
                        response.IsInflationNominatedCaseEdit = false;
                        EntityCollection inflationEditRecords = service.RetrieveMultiple(new FetchExpression(FetchInflationXml));
                        if (inflationEditRecords.Entities != null && inflationEditRecords.Entities.Count > 0)
                        {
                            Entity inflationEntity = inflationEditRecords.Entities.FirstOrDefault();
                            int inflationType = inflationEntity.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_InflationType).Value;
                            caseResponse.InflationType = inflationType;
                            if (inflationType == (int)hexa_Request_mocd_InflationType.StandAlone)
                            {
                                response.IsInflationStandAloneEdit = true;
                            }
                            else if (inflationType == (int)hexa_Request_mocd_InflationType.BaseAllowanceInflation)
                            {
                                response.IsInflationBaseEdit = true;
                            }
                            else if (inflationType == (int)hexa_Request_mocd_InflationType.NominatedbySocialSupportEntity)
                            {
                                response.IsInflationNominatedCaseEdit = true;
                            }
                        }

                        #endregion
                    }
                    caseResponse.ListIncomeSourceDetails = IncomeSourceDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new IncomeSourceDetails { IdIncome = x.IdIncome, IncomeAmount = x.IncomeAmount, CompanyName = x.CompanyName, IncomeSource = x.Income }).ToList();
                    caseResponse.ListPensionDetails = PensionDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new PensionDetails { IdPension = x.IdPension, IncomeAmount = x.IncomeAmount, PensionAuthority = x.PensionAuthority, PensionType = x.PensionType }).ToList();
                    caseResponse.ListTradeLicenseDetails = TradeLicenseDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new TradeLicenseDetails { IdTradeLicense = x.IdTradeLicense, IncomeAmount = x.IncomeAmount }).ToList();
                    caseResponse.ListRentalDetails = GetRentalSource(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new RentalDetails { IdRental = x.IdRental, IncomeAmount = x.IncomeAmount, ContractEndDate = x.ContractEndDate, ContractNumber = x.ContractNumber, ContractStartDate = x.ContractStartDate, RentalSource = x.RentalSource }).ToList();
                    caseResponse.ListFamilyMember = GetFamilybookByCaseId(caseId, service, IsSpouseOfIncapForeign: (caseResponse.SubCategory == new Guid("163eb27d-9257-ee11-be6f-6045bd14ccdc") ? true : false));
                    caseResponse.ListofChildren = GetChildrenByCaseId(caseId, service);
                    response.CaseDetails = caseResponse;


                    return response;
                }
                else
                {
                    throw new Exception(CustomMessages.CasePerimission);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseRequestByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

        }
        private static string GetEmiratesIdByContactId(IOrganizationService _gblService, Guid ContactId)
        {
            try
            {
                EntityCollection records = _gblService.RetrieveMultiple(new FetchExpression(FetchXML.GetEmiratesIdByContactIdXML(ContactId)));
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();
                    return entity.Contains(Contact.Fields.mocd_EmiratesID) ? entity.GetAttributeValue<string>(Contact.Fields.mocd_EmiratesID) : string.Empty;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        private static List<IncomeSource> IncomeSourceDetailsByCaseId(Guid id, IOrganizationService service)
        {
            Logger.Info("Inside IncomeSourceDetailsByCaseId method");
            Logger.Info("IncomeSourceDetailsByCaseId " + JsonConvert.SerializeObject("Id: " + id));
            try
            {
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.IncomeSourceDetailsByCaseIdXML(id)));
                List<IncomeSource> incomeSourcesDetails = new List<IncomeSource>();
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        IncomeSource incomeSourceDetails = new IncomeSource
                        {
                            IdIncome = entity.Id,
                            FamilybookId = entity.Contains(mocd_casebeneficiaryincome.Fields.mocd_CaseFamilyBook) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryincome.Fields.mocd_CaseFamilyBook).Id : Guid.Empty,
                            IncomeAmount = entity.Contains(mocd_casebeneficiaryincome.Fields.mocd_Income) ? entity.GetAttributeValue<Money>(mocd_casebeneficiaryincome.Fields.mocd_Income).Value : default(decimal),
                            CompanyName = entity.Contains(mocd_casebeneficiaryincome.Fields.mocd_CompanyName) ? entity.GetAttributeValue<string>(mocd_casebeneficiaryincome.Fields.mocd_CompanyName) : string.Empty,
                            Income = entity.Contains(mocd_casebeneficiaryincome.Fields.mocd_IncomeSource) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryincome.Fields.mocd_IncomeSource).Id : Guid.Empty
                        };
                        incomeSourcesDetails.Add(incomeSourceDetails);
                    }
                }
                return incomeSourcesDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from IncomeSourceDetailsByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }


        private static List<Pension> PensionDetailsByCaseId(Guid id, IOrganizationService service)
        {
            Logger.Info("Inside PensionDetailsByCaseId method");
            Logger.Info("PensionDetailsByCaseId " + JsonConvert.SerializeObject("Id: " + id));
            try
            {
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.PensionDetailsByCaseIdXML(id)));
                List<Pension> pensionsDetails = new List<Pension>();
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        Pension pensionDetails = new Pension
                        {
                            IdPension = entity.Id,
                            FamilybookId = entity.Contains(mocd_casebeneficiarypension.Fields.mocd_CaseFamilyBook) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiarypension.Fields.mocd_CaseFamilyBook).Id : Guid.Empty,
                            IncomeAmount = entity.Contains(mocd_casebeneficiarypension.Fields.mocd_PensionAmount) ? entity.GetAttributeValue<Money>(mocd_casebeneficiarypension.Fields.mocd_PensionAmount).Value : default(decimal),
                            PensionAuthority = entity.Contains(mocd_casebeneficiarypension.Fields.mocd_PensionAuthority) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiarypension.Fields.mocd_PensionAuthority).Id : Guid.Empty,
                            PensionType = entity.Contains(mocd_casebeneficiarypension.Fields.mocd_PensionType) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiarypension.Fields.mocd_PensionType).Id : Guid.Empty,
                        };
                        pensionsDetails.Add(pensionDetails);
                    }
                }
                return pensionsDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from PensionDetailsByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        private static List<TradeLicense> TradeLicenseDetailsByCaseId(Guid id, IOrganizationService service)
        {
            Logger.Info("Inside TradeLicenseDetailsByCaseId method");
            Logger.Info("TradeLicenseDetailsByCaseId " + JsonConvert.SerializeObject("Id: " + id));
            try
            {
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.TradeLicenseDetailsByCaseIdXML(id)));
                List<TradeLicense> tradeLicensesDetails = new List<TradeLicense>();
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        TradeLicense tradeLicenseDetails = new TradeLicense
                        {
                            IdTradeLicense = entity.Id,
                            FamilybookId = entity.Contains(mocd_casebeneficiarytradelicense.Fields.mocd_CaseFamilyBook) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiarytradelicense.Fields.mocd_CaseFamilyBook).Id : Guid.Empty,
                            IncomeAmount = entity.Contains(mocd_casebeneficiarytradelicense.Fields.mocd_Income) ? entity.GetAttributeValue<Money>(mocd_casebeneficiarytradelicense.Fields.mocd_Income).Value : default(decimal),

                        };
                        tradeLicensesDetails.Add(tradeLicenseDetails);
                    }
                }
                return tradeLicensesDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from TradeLicenseDetailsByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<FamilyMemberDetails> GetFamilybookByCaseId(Guid id, IOrganizationService service, bool IsFarmerService = false, bool IsSpouseOfIncapForeign = false, bool isPODChild = false)
        {
            Logger.Info("Inside GetFamilybookByCaseId method");
            Logger.Info("GetFamilybookByCaseId " + JsonConvert.SerializeObject("Id: " + id));
            try
            {
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.FamilybookByCaseIdXML(id, IsSpouseOfIncapForeign, isPODChild)));
                List<FamilyMemberDetails> familyMembersDetails = new List<FamilyMemberDetails>();
                Guid familybookId = Guid.Empty;
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        familybookId = entity.Id;
                        FamilyMemberDetails familyMemberDetails = new FamilyMemberDetails
                        {
                            Id = entity.Id,
                            FullnameAR = entity.Contains("aliasContactDependent.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_fullnamearabic").Value.ToString() : string.Empty,
                            FullnameEN = entity.Contains("aliasContactDependent.fullname") ? entity.GetAttributeValue<AliasedValue>("aliasContactDependent.fullname").Value.ToString() : string.Empty,
                            IdDependentBeneficary = entity.Contains(mocd_casefamilybook.Fields.mocd_Dependent) ? entity.GetAttributeValue<EntityReference>(mocd_casefamilybook.Fields.mocd_Dependent).Id : Guid.Empty,
                            Relationship = entity.Contains(mocd_casefamilybook.Fields.mocd_RelationshiptoFamilyHead) ? entity.GetAttributeValue<EntityReference>(mocd_casefamilybook.Fields.mocd_RelationshiptoFamilyHead).Id : Guid.Empty,
                            IsFamilyMemberContributeToIncome = entity.Contains(mocd_casefamilybook.Fields.mocd_FamilyMemberContributestoIncome) ? entity.GetAttributeValue<bool>(mocd_casefamilybook.Fields.mocd_FamilyMemberContributestoIncome) : false,
                            IsFamilyMemberReceivePensionIncome = entity.Contains(mocd_casefamilybook.Fields.mocd_FamilyMemberRetirementorPension) ? entity.GetAttributeValue<bool>(mocd_casefamilybook.Fields.mocd_FamilyMemberRetirementorPension) : false,
                            IsFamilyMemberReceiveTradeLicense = entity.Contains(mocd_casefamilybook.Fields.mocd_FamilyHeadTradeLicense) ? entity.GetAttributeValue<bool>(mocd_casefamilybook.Fields.mocd_FamilyHeadTradeLicense) : false,
                            IsInformationUpdated = entity.Contains(mocd_casefamilybook.Fields.mocd_IsInformationComplete) ? entity.GetAttributeValue<bool>(FamilyBookColumns.Isinformationcomplete) : false,
                            IsFamilyMemberReceiveRentalIncome = entity.Contains(mocd_casefamilybook.Fields.mocd_FamilyMemberContributestoRentalIncome) ? entity.GetAttributeValue<bool>(mocd_casefamilybook.Fields.mocd_FamilyMemberContributestoRentalIncome) : false,
                            FamilyHeadAR = entity.Contains("aliasContactHead.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("aliasContactHead.mocd_fullnamearabic").Value.ToString() : string.Empty,
                            FamilyHeadEN = entity.Contains("aliasContactHead.fullname") ? entity.GetAttributeValue<AliasedValue>("aliasContactHead.fullname").Value.ToString() : string.Empty,
                            KhulasitQaidNumber = entity.Contains("aliasContactHead.mocd_khulasitqaidnumber") ? entity.GetAttributeValue<AliasedValue>("aliasContactHead.mocd_khulasitqaidnumber").Value.ToString() : string.Empty,
                            Occupations = entity.Contains("mocd_occupation") ? entity.GetAttributeValue<EntityReference>("mocd_occupation").Id : entity.Contains("aliasContactHead.mocd_occupation") ? ((EntityReference)(entity.GetAttributeValue<AliasedValue>("aliasContactHead.mocd_occupation").Value)).Id : Guid.Empty,
                        };

                        if (!IsFarmerService)
                        {
                            Guid caseInternalStatus = Guid.Empty;
                            EntityCollection CaseRecords = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseByCaseIdXML(id)));
                            if (CaseRecords.Entities != null && CaseRecords.Entities.Count > 0)
                            {
                                Entity CaseEntity = CaseRecords.Entities.FirstOrDefault();
                                caseInternalStatus = CaseEntity.Contains(hexa_Request.Fields.hexa_InternalStatus) ? CaseEntity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;
                            }

                            KeyValuePair<string, string> wifeFamilyRelationShipGuid = HexaConfiguration.FetchDataFromConfiguration("FamilyBook-Wife", service);
                            KeyValuePair<string, string> pendingCaseInternalStatusGuid = HexaConfiguration.FetchDataFromConfiguration("CaseInternalStatus-PendingwithCustomer", service);

                            if (familyMemberDetails.Relationship != Guid.Empty && familyMemberDetails.Relationship.ToString().ToUpper() == wifeFamilyRelationShipGuid.Value.ToUpper() &&
                                caseInternalStatus != Guid.Empty && caseInternalStatus.ToString().ToUpper() == pendingCaseInternalStatusGuid.Value.ToUpper())
                            {
                                familyMemberDetails.IsInformationUpdated = true;
                            }
                        }
                        familyMemberDetails.ListIncomeSourceDetails = IncomeSourceDetailsByCaseId(id, service).Where(x => x.FamilybookId == familybookId).Select(x => new IncomeSourceDetails { IdIncome = x.IdIncome, IncomeAmount = x.IncomeAmount, IncomeSource = x.Income, CompanyName = x.CompanyName }).ToList();
                        familyMemberDetails.ListPensionDetails = PensionDetailsByCaseId(id, service).Where(x => x.FamilybookId == familybookId).Select(x => new PensionDetails { IdPension = x.IdPension, IncomeAmount = x.IncomeAmount, PensionAuthority = x.PensionAuthority, PensionType = x.PensionType }).ToList();
                        familyMemberDetails.ListTradeLicenseDetails = TradeLicenseDetailsByCaseId(id, service).Where(x => x.FamilybookId == familybookId).Select(x => new TradeLicenseDetails { IdTradeLicense = x.IdTradeLicense, IncomeAmount = x.IncomeAmount }).ToList();
                        familyMemberDetails.ListRentalDetails = GetRentalSource(id, service).Where(x => x.FamilybookId == familybookId).Select(x => new RentalDetails { IdRental = x.IdRental, IncomeAmount = x.IncomeAmount, ContractEndDate = x.ContractEndDate, ContractNumber = x.ContractNumber, ContractStartDate = x.ContractStartDate, RentalSource = x.RentalSource }).ToList();
                        familyMembersDetails.Add(familyMemberDetails);
                    }
                }
                return familyMembersDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetFamilybookByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<ChildrenDetails> GetChildrenByCaseId(Guid id, IOrganizationService service)
        {
            Logger.Info("Inside GetChildrenByCaseId method");
            Logger.Info("GetChildrenByCaseId " + JsonConvert.SerializeObject("Id: " + id));
            try
            {
                //retrieve daughter and chil Guid
                KeyValuePair<string, string> SonGuid = HexaConfiguration.FetchDataFromConfiguration("FamilyBook-son", service);
                KeyValuePair<string, string> DaughterGuid = HexaConfiguration.FetchDataFromConfiguration("FamilyBook-daughter", service);
                KeyValuePair<string, string> BrotherGuid = HexaConfiguration.FetchDataFromConfiguration("FamilyBook-brother", service);
                KeyValuePair<string, string> SisterGuid = HexaConfiguration.FetchDataFromConfiguration("FamilyBook-sister", service);

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.FamilybookChildrenByCaseIdXML(id, SonGuid.Value, DaughterGuid.Value, BrotherGuid.Value, SisterGuid.Value)));
                List<ChildrenDetails> childrenDetails = new List<ChildrenDetails>();
                Guid familybookId = Guid.Empty;
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {
                        familybookId = entity.Id;

                        string allowanceAge = entity.Contains("aliasContactDependent.mocd_allowancegroupage") ? entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_allowancegroupage")?.Value?.ToString() ?? string.Empty : string.Empty;

                        DateTime? birthDate = null;
                        if (entity.Contains("aliasContactDependent.mocd_dateofbirth"))
                        {
                            birthDate = entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_dateofbirth")?.Value as DateTime?;
                        }

                        int? calculatedAge = null;
                        if (birthDate.HasValue)
                        {
                            var now = DateTime.Now;
                            calculatedAge = now.Year - birthDate.Value.Year;
                            if (birthDate.Value > now.AddYears(-calculatedAge.Value))
                                calculatedAge--;
                        }

                        string finalAge = allowanceAge;
                        bool needsUpdate = false;

                        if (string.IsNullOrWhiteSpace(allowanceAge) || allowanceAge == "0")
                        {
                            if (calculatedAge.HasValue)
                            {
                                finalAge = calculatedAge.Value.ToString();
                                needsUpdate = true;
                            }
                        }
                        else if (calculatedAge.HasValue &&
                                 int.TryParse(allowanceAge, out int storedAge) &&
                                 storedAge != calculatedAge.Value)
                        {
                            finalAge = calculatedAge.Value.ToString();
                            needsUpdate = true;
                        }

                        if (needsUpdate && calculatedAge.HasValue &&
                            entity.Contains("aliasContactDependent.contactid") &&
                            entity["aliasContactDependent.contactid"] is AliasedValue contactIdValue &&
                            Guid.TryParse(contactIdValue.Value?.ToString(), out Guid contactId))
                        {
                            try
                            {
                                Entity contactEntity = service.Retrieve("contact", contactId, new ColumnSet("mocd_allowancegroupage"));
                                if (contactEntity != null)
                                {
                                    var contactToUpdate = contactEntity.ToEntity<Contact>();
                                    contactToUpdate.mocd_AllowanceGroupAge = calculatedAge.Value;
                                    service.Update(contactToUpdate);
                                }
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"Error updating age for contact {contactId}: {ex.Message}", ex);
                            }
                        }

                        ChildrenDetails ChildrenDetail = new ChildrenDetails
                        {
                            Id = entity.Id,
                            IdDependentBeneficary = entity.Contains(mocd_casefamilybook.Fields.mocd_Dependent) ? entity.GetAttributeValue<EntityReference>(mocd_casefamilybook.Fields.mocd_Dependent).Id : Guid.Empty,
                            IsDraftedinMilitaryService = entity.Contains("aliasContactDependent.mocd_draftedinmilitaryservice") ? (bool?)entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_draftedinmilitaryservice").Value : false,
                            IsPursuingHigherEducation = entity.Contains("aliasContactDependent.mocd_pursuinghighereducation") ? (bool?)entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_pursuinghighereducation").Value : false,
                            Age = finalAge,
                            FullNameAR = entity.Contains("aliasContactDependent.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_fullnamearabic").Value.ToString() : string.Empty,
                            FullNameEN = entity.Contains("aliasContactDependent.lastname") ? entity.GetAttributeValue<AliasedValue>("aliasContactDependent.lastname").Value.ToString() : string.Empty,
                            Occupations = entity.Contains("aliasContactDependent.mocd_occupation") ? ((EntityReference)(entity.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_occupation").Value)).Id : Guid.Empty,
                        };
                        childrenDetails.Add(ChildrenDetail);
                    }

                }
                return childrenDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetFamilybookByCaseId: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static List<Rental> GetRentalSource(Guid id, IOrganizationService service)
        {
            try
            {
                Logger.Info("Inside GetRentalSource method");
                Logger.Info("GetRentalSource " + JsonConvert.SerializeObject("Id: " + id));
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetRentalSourceByCaseIdXML(id)));
                List<Rental> rentalSources = new List<Rental>();
                mocd_casebeneficiaryrentalincome mocd_Casebeneficiaryrentalincome = new mocd_casebeneficiaryrentalincome();
                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (Entity entity in records.Entities)
                    {


                        Rental rentalDetails = new Rental
                        {
                            IncomeAmount = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_RentAmount) ? entity.GetAttributeValue<Money>(mocd_casebeneficiaryrentalincome.Fields.mocd_RentAmount).Value : default(decimal),
                            ContractEndDate = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractEndDate) ? entity.GetAttributeValue<DateTime>(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractEndDate).ToLocalTime().ToString("o", CultureInfo.InvariantCulture) : string.Empty,
                            ContractStartDate = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractStartDate) ? entity.GetAttributeValue<DateTime>(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractStartDate).ToLocalTime().ToString("o", CultureInfo.InvariantCulture) : string.Empty,
                            ContractNumber = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractNo) ? entity.GetAttributeValue<string>(mocd_casebeneficiaryrentalincome.Fields.mocd_ContractNo) : string.Empty,
                            IdRental = entity.Id,
                            FamilybookId = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_CaseFamilyBook) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryrentalincome.Fields.mocd_CaseFamilyBook).Id : Guid.Empty,
                            RentalSource = entity.Contains(mocd_casebeneficiaryrentalincome.Fields.mocd_RentalSource) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryrentalincome.Fields.mocd_RentalSource).Id : Guid.Empty,

                        };
                        rentalSources.Add(rentalDetails);
                    }
                }
                return rentalSources;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetRentalSource: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static CasePersonalDetails GetCasePersonalDetails(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCasePersonalDetails method");
            Logger.Info("GetCasePersonalDetails " + JsonConvert.SerializeObject("Case Id: " + caseId + " beneficiary Id: " + beneficiaryId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);
                if (beneficiaryId == Guid.Empty)
                    throw new Exception(CustomMessages.BeneficiaryId);
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCasePersonalDetailsRequestByIdXML(caseId, beneficiaryId)));
                CasePersonalDetails response = new CasePersonalDetails();

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();

                    response.AccomodationType = entity.Contains(hexa_Request.Fields.mocd_AccommodationType) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_AccommodationType).Id : Guid.Empty;
                    response.Occupation = entity.Contains(hexa_Request.Fields.mocd_Occupation) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_Occupation).Id : Guid.Empty;
                    response.Education = entity.Contains(hexa_Request.Fields.mocd_Education) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_Education).Id : Guid.Empty;
                    response.JobTitle = entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_JobTitle);
                    response.Alternativenumber = entity.Contains(hexa_Request.Fields.mocd_AlternatePhone) ? entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_AlternatePhone) : string.Empty;
                    response.AlternativeEmail = entity.Contains(hexa_Request.Fields.EmailAddress) ? entity.GetAttributeValue<string>(hexa_Request.Fields.EmailAddress) : string.Empty;
                    response.CaseRef = entity.Contains(hexa_Request.Fields.hexa_Name) ? entity.GetAttributeValue<string>(hexa_Request.Fields.hexa_Name) : string.Empty;
                    response.Address = entity.Contains(hexa_Request.Fields.mocd_Address) ? entity.GetAttributeValue<string>(hexa_Request.Fields.mocd_Address) : string.Empty;
                    response.MaritalStatus = entity.Contains(hexa_Request.Fields.mocd_MaritalStatus) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_MaritalStatus).Id : Guid.Empty;
                    response.Emirate = entity.Contains(hexa_Request.Fields.mocd_emiratecode) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_emiratecode).Id : Guid.Empty;
                    response.Area = entity.Contains(hexa_Request.Fields.mocd_area) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_area).Id : Guid.Empty;
                    response.IdCase = entity.Id;

                    return response;
                }
                else
                {
                    throw new Exception(CustomMessages.CasePerimission);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCasePersonalDetails: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

        }

        public static CaseFamilyHeadDetails GetCaseFamilyHeadListDetails(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseFamilyHeadListDetails method");
            Logger.Info("GetCaseFamilyHeadListDetails " + JsonConvert.SerializeObject("Case Id: " + caseId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseFamilyHeadRequestByIdXML(caseId, beneficiaryId)));
                CaseFamilyHeadDetails response = new CaseFamilyHeadDetails();

                response.IdCase = caseId;

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();

                    response.IsHouseholdHeadContributeToIncome = entity.Contains(hexa_Request.Fields.mocd_householdcontributestoincome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdcontributestoincome) : false;
                    response.IshouseholdHeadTradeLicense = entity.Contains(hexa_Request.Fields.mocd_householdtradelicense) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdtradelicense) : false;
                    response.IsHouseholdHeadReceivePensionIncome = entity.Contains(hexa_Request.Fields.mocd_householdretirementorpensionincome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_householdretirementorpensionincome) : false;
                    response.IshouseholdHeadReceiveRentalIncome = entity.Contains(hexa_Request.Fields.mocd_DoesthehouseholdheadreceivearentalIncome) ? entity.GetAttributeValue<bool>(hexa_Request.Fields.mocd_DoesthehouseholdheadreceivearentalIncome) : false;

                }

                response.ListIncomeSourceDetails = IncomeSourceDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new IncomeSourceDetails { IdIncome = x.IdIncome, IncomeAmount = x.IncomeAmount, CompanyName = x.CompanyName, IncomeSource = x.Income }).ToList();
                response.ListPensionDetails = PensionDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new PensionDetails { IdPension = x.IdPension, IncomeAmount = x.IncomeAmount, PensionAuthority = x.PensionAuthority, PensionType = x.PensionType }).ToList();
                response.ListTradeLicenseDetails = TradeLicenseDetailsByCaseId(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new TradeLicenseDetails { IdTradeLicense = x.IdTradeLicense, IncomeAmount = x.IncomeAmount }).ToList();
                response.ListRentalDetails = GetRentalSource(caseId, service).Where(y => y.FamilybookId == Guid.Empty).Select(x => new RentalDetails { IdRental = x.IdRental, IncomeAmount = x.IncomeAmount, ContractEndDate = x.ContractEndDate, ContractNumber = x.ContractNumber, ContractStartDate = x.ContractStartDate, RentalSource = x.RentalSource }).ToList();

                return response;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseFamilyHeadListDetails: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static void DeleteEducationalAllowance(Guid caseId, List<ChildrenDetails> ListofChildren, IOrganizationService service)
        {
            string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='mocd_casebeneficiaryeducationalallowance'>
                                    <attribute name='mocd_casebeneficiaryeducationalallowanceid' />
                                    <attribute name='mocd_name' />
                                    <attribute name='mocd_beneficiary' />
                                    <order attribute='mocd_name' descending='false' />
                                    <filter type='and'>
                                      <condition attribute='mocd_case' operator='eq' uiname='74235' uitype='hexa_request' value='" + caseId + @"' />
                                    </filter>
                                  </entity>
                                </fetch>";

            EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

            if (records.Entities != null && records.Entities.Count > 0)
            {
                foreach (var entity in records.Entities)
                {
                    Guid ContactId = entity.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary).Id : Guid.Empty;
                    List<ChildrenDetails> filteredListofChildren = null;

                    if (ListofChildren != null)
                    {
                        filteredListofChildren = ListofChildren.Where(x => x.IdDependentBeneficary == ContactId).ToList();
                    }
                    if (!(filteredListofChildren != null && filteredListofChildren.Count > 0))
                    {
                        service.Delete(mocd_casebeneficiaryeducationalallowance.EntityLogicalName, entity.Id);
                    }
                }
            }
        }

        public static void CreateEducationalAllowance(Guid caseId, List<ChildrenDetails> ListofChildren, IOrganizationService service)
        {
            ListofChildren.RemoveAll(x => string.IsNullOrEmpty(x.Age));
            List<ChildrenDetails> filteredListofChildren = ListofChildren.Where(x => int.Parse(x.Age) >= 16 && int.Parse(x.Age) <= 24).ToList();

            if (filteredListofChildren != null && filteredListofChildren.Count > 0)
            {
                string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='mocd_casebeneficiaryeducationalallowance'>
                                    <attribute name='mocd_casebeneficiaryeducationalallowanceid' />
                                    <attribute name='mocd_name' />
                                    <attribute name='mocd_beneficiary' />
                                    <order attribute='mocd_name' descending='false' />
                                    <filter type='and'>
                                      <condition attribute='mocd_case' operator='eq' uiname='74235' uitype='hexa_request' value='" + caseId + @"' />
                                    </filter>
                                  </entity>
                                </fetch>";

                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    foreach (var entity in records.Entities)
                    {
                        Guid ContactId = entity.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary) ? entity.GetAttributeValue<EntityReference>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary).Id : Guid.Empty;
                        filteredListofChildren.RemoveAll(x => x.IdDependentBeneficary == ContactId);
                    }
                }
                if (filteredListofChildren != null && filteredListofChildren.Count > 0)
                {
                    foreach (ChildrenDetails educationCase in filteredListofChildren)
                    {
                        mocd_casebeneficiaryeducationalallowance objEducationalAllowance = new mocd_casebeneficiaryeducationalallowance
                        {
                            mocd_case = new EntityReference(hexa_Request.EntityLogicalName, caseId),
                            mocd_beneficiary = new EntityReference(Contact.EntityLogicalName, educationCase.IdDependentBeneficary),
                            mocd_name = educationCase.FullNameEN,
                            mocd_namear = educationCase.FullNameAR,
                            mocd_applyforeducationalexcellenceallowancefor = false,
                            mocd_iscompletedfromportal = false,
                        };

                        service.Create(objEducationalAllowance);
                    }
                }
            }

        }


        public static CaseFamilyMembersDetails GetCaseFamilyMembersListDetails(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseFamilyMembersListDetails method");
            Logger.Info("GetCaseFamilyMembersListDetails " + JsonConvert.SerializeObject("Case Id: " + caseId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);

                CaseFamilyMembersDetails response = new CaseFamilyMembersDetails();

                response.IdCase = caseId;
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseRequestByIdXML(caseId, beneficiaryId)));

                Entity entity = service.Retrieve(hexa_Request.EntityLogicalName, caseId, new ColumnSet(true));
                var SubCategory = entity.Contains(hexa_Request.Fields.mocd_subcategory) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_subcategory).Id : Guid.Empty;

                bool IsSpouseOfIncapForeign = (SubCategory == new Guid("163eb27d-9257-ee11-be6f-6045bd14ccdc"));
                bool isPODChild = CaseHelper.IsPODChild(service, caseId);

                response.ListFamilyMember = GetFamilybookByCaseId(caseId, service, false, IsSpouseOfIncapForeign, isPODChild);
                response.ListofChildren = GetChildrenByCaseId(caseId, service);
                //response.FamilyHeadNameAR = response.ListFamilyMember.Select(person => person.FamilyHeadAR).FirstOrDefault();
                //response.FamilyHeadNameEN = response.ListFamilyMember.Select(person => person.FamilyHeadEN).FirstOrDefault();
                // Get relationship type GUIDs for father and mother
                Guid fatherRelationshipGuid = new Guid("49fd9b8d-6a75-ed11-81ad-002248cbd873");
                Guid motherRelationshipGuid = new Guid("51fd9b8d-6a75-ed11-81ad-002248cbd873");

                // Find family head based on relationship priority: father > mother > existing logic
                var fatherMember = response.ListFamilyMember.FirstOrDefault(m => m.Relationship == fatherRelationshipGuid);
                var motherMember = response.ListFamilyMember.FirstOrDefault(m => m.Relationship == motherRelationshipGuid);

                if (fatherMember != null)
                {
                    response.FamilyHeadNameAR = fatherMember.FullnameAR;
                    response.FamilyHeadNameEN = fatherMember.FullnameEN;
                }
                else if (motherMember != null)
                {
                    response.FamilyHeadNameAR = motherMember.FullnameAR;
                    response.FamilyHeadNameEN = motherMember.FullnameEN;
                }
                else
                {
                    // Use existing logic as fallback
                    response.FamilyHeadNameAR = response.ListFamilyMember.Select(person => person.FamilyHeadAR).FirstOrDefault();
                    response.FamilyHeadNameEN = response.ListFamilyMember.Select(person => person.FamilyHeadEN).FirstOrDefault();
                }

                response.KhulasitQaidNumber = response.ListFamilyMember.Select(person => person.KhulasitQaidNumber).FirstOrDefault();

                return response;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseFamilyMembersListDetails: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static CaseFamilyMembersDetails GetCaseFamilyMembersListDetailsFarmerService(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseFamilyMembersListDetails method");
            Logger.Info("GetCaseFamilyMembersListDetails " + JsonConvert.SerializeObject("Case Id: " + caseId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);

                CaseFamilyMembersDetails response = new CaseFamilyMembersDetails();

                response.IdCase = caseId;
                response.ListFamilyMember = GetFamilybookByCaseId(caseId, service, true);
                response.ListofChildren = GetChildrenByCaseId(caseId, service);

                return response;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseFamilyMembersListDetails: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static CaseSummaryDetails GetCaseSummaryDetails(Guid caseId, Guid beneficiaryId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseSummaryDetails method");
            Logger.Info("GetCaseSummaryDetails " + JsonConvert.SerializeObject("Case Id: " + caseId + " beneficiary Id: " + beneficiaryId));
            try
            {
                if (caseId == Guid.Empty)
                    throw new Exception(CustomMessages.CaseId);
                if (beneficiaryId == Guid.Empty)
                    throw new Exception(CustomMessages.BeneficiaryId);
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseSummaryRequestByIdXML(caseId, beneficiaryId)));

                CaseSummaryDetails response = new CaseSummaryDetails();

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();

                    response.IdCase = entity.Id;
                    response.CaseRef = entity.Contains(hexa_Request.Fields.hexa_Name) ? entity.GetAttributeValue<string>(hexa_Request.Fields.hexa_Name) : string.Empty;
                    response.SubmissionTime = entity.Contains(hexa_Request.Fields.hexa_SubmittedOn) ? entity.GetAttributeValue<DateTime>(hexa_Request.Fields.hexa_SubmittedOn).ToLocalTime().ToString("o", CultureInfo.InvariantCulture) : string.Empty;
                    response.NameEn = entity.Contains("aliasContact.fullname") ? entity.GetAttributeValue<AliasedValue>("aliasContact.fullname").Value.ToString() : string.Empty;
                    response.NameAr = entity.Contains("aliasContact.mocd_fullnamearabic") ? entity.GetAttributeValue<AliasedValue>("aliasContact.mocd_fullnamearabic").Value.ToString() : string.Empty;
                    //response.Email = entity.Contains("aliasContact.emailaddress2") ? entity.GetAttributeValue<AliasedValue>("aliasContact.emailaddress2").Value.ToString() : string.Empty;
                    response.Email = entity.Contains("emailaddress") ? entity.GetAttributeValue<string>("emailaddress") : string.Empty;
                    //response.Mobile = entity.Contains("aliasContact.telephone2") ? entity.GetAttributeValue<AliasedValue>("aliasContact.telephone2").Value.ToString() : string.Empty;
                    response.Mobile = entity.Contains("mocd_alternatephone") ? entity.GetAttributeValue<string>("mocd_alternatephone") : string.Empty;
                    response.ProcessTemplate = entity.Contains(hexa_Request.Fields.hexa_ProcessTemplate) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_ProcessTemplate).Id : Guid.Empty;

                    return response;
                }
                else
                {
                    throw new Exception(CustomMessages.CasePerimission);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseSummaryDetails: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

        }

        public static object GetCaseEducationalAllowance(Guid caseId, IOrganizationService service)
        {
            Logger.Info("Inside GetCaseEducationalAllowance method");
            Logger.Info("GetCaseEducationalAllowance " + JsonConvert.SerializeObject("Case Id: " + caseId));
            try
            {
                EntityCollection educationalAllowanceRecords = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseBeneficiaryEducationalAllowanceXML(caseId)));
                List<EducationCase> EducationCaseDetails = new List<EducationCase>();
                List<MasterResponse> universities = MasterService.GetUniversities(service);

                if (educationalAllowanceRecords.Entities != null && educationalAllowanceRecords.Entities.Count > 0)
                {
                    foreach (mocd_casebeneficiaryeducationalallowance educationalAllowanc in educationalAllowanceRecords.Entities)
                    {
                        EducationCase objEducationCase = new EducationCase();
                        objEducationCase.FullNameAr = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_namear) ? educationalAllowanc.GetAttributeValue<string>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_namear) : string.Empty;
                        objEducationCase.FullNameEn = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_name) ? educationalAllowanc.GetAttributeValue<string>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_name) : string.Empty;
                        objEducationCase.IdChild = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary) ? educationalAllowanc.GetAttributeValue<EntityReference>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_beneficiary).Id : Guid.Empty;
                        objEducationCase.IdCaseBeneficiaryEducationalAllowance = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_casebeneficiaryeducationalallowanceId) ? educationalAllowanc.GetAttributeValue<Guid>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_casebeneficiaryeducationalallowanceId) : Guid.Empty;
                        objEducationCase.enrolledEducationStream = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_publiceducationstream) ? educationalAllowanc.GetAttributeValue<OptionSetValue>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_publiceducationstream).Value : (int?)null;
                        objEducationCase.EmSATorAdvancedPlacementScores = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_uploademsatoradvancedplacementscores) ? educationalAllowanc.GetAttributeValue<OptionSetValue>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_uploademsatoradvancedplacementscores).Value : (int?)null;
                        objEducationCase.highSchoolCurriculuim = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_highschoolcurriculum) ? educationalAllowanc.GetAttributeValue<OptionSetValue>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_highschoolcurriculum).Value : (int?)null;
                        // Set to true to enable education allowance application for all users
                        objEducationCase.ApplyEducationAllowance = true; // educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_applyforeducationalexcellenceallowancefor) ? educationalAllowanc.GetAttributeValue<bool>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_applyforeducationalexcellenceallowancefor) : false;
                        objEducationCase.childCompletedSemesterInUniversity = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_hascompletedmorethan1semesterinuniversity) ? educationalAllowanc.GetAttributeValue<bool?>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_hascompletedmorethan1semesterinuniversity) : null;

                        objEducationCase.IsCompletedFromPortal = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_iscompletedfromportal) ? educationalAllowanc.GetAttributeValue<bool>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_iscompletedfromportal) : false;

                        objEducationCase.Age = educationalAllowanc.Contains("aliasContactDependent.mocd_allowancegroupage")
                                ? educationalAllowanc.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_allowancegroupage")?.Value?.ToString() : string.Empty;

                        objEducationCase.IsEnrolledInNationalService = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_isenrolledinnationalservice) ? educationalAllowanc.GetAttributeValue<bool?>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_isenrolledinnationalservice) : null;

                        objEducationCase.cgpa = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_universitycgpa) ? educationalAllowanc.GetAttributeValue<double?>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_universitycgpa) : (double?)null;
                        objEducationCase.creditHours = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_credithours) ? educationalAllowanc.GetAttributeValue<double?>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_credithours) : (double?)null;
                        objEducationCase.universityName = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_universityname) ? educationalAllowanc.GetAttributeValue<EntityReference>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_universityname).Id : Guid.Empty;
                        objEducationCase.unaccreditedUniversityName = educationalAllowanc.Contains(mocd_casebeneficiaryeducationalallowance.Fields.mocd_unaccrediteduniversityname) ? educationalAllowanc.GetAttributeValue<string>(mocd_casebeneficiaryeducationalallowance.Fields.mocd_unaccrediteduniversityname) : String.Empty;

                        var emiratesId = educationalAllowanc.Contains("aliasContactDependent.mocd_emiratesid") ? educationalAllowanc.GetAttributeValue<AliasedValue>("aliasContactDependent.mocd_emiratesid").Value.ToString() : string.Empty;

                        if (!string.IsNullOrEmpty(emiratesId) && educationalAllowanc.mocd_iscompletedfromportal == false)
                        {
                            try
                            {
                                MohesrResponse resp = MohesrService.GetMohesrStudentInfoNonAsync(service, emiratesId);

                                if (resp != null && resp.List != null && resp.List.Count > 0)
                                {
                                    var student = resp.List.First();

                                    var matchedUniversity = universities.FirstOrDefault(u =>
                                        string.Equals(u.Name?.Trim(), student.Institution_Name?.Trim(), StringComparison.OrdinalIgnoreCase) ||
                                        string.Equals(u.NameAR?.Trim(), student.Institution_Name?.Trim(), StringComparison.OrdinalIgnoreCase)
                                    );

                                    if (matchedUniversity != null)
                                    {
                                        objEducationCase.universityName = matchedUniversity.Id;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                        }


                        EducationCaseDetails.Add(objEducationCase);
                    }

                    #region case type
                    string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                      <entity name='hexa_request'>
                                        <attribute name='hexa_name' />
                                        <attribute name='hexa_requestid' />
                                        <attribute name='hexa_portalcontact' />
                                        <attribute name='mocd_casetype' />
                                        <order attribute='hexa_name' descending='true' />
                                        <filter type='and'>
                                          <condition attribute='hexa_requestid' operator='eq' uiname='74235' uitype='hexa_request' value='" + caseId + @"' />
                                        </filter>
                                      </entity>
                                    </fetch>";

                    Entity caseRecord = service.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    int caseType = 1;

                    if (caseRecord != null)
                    {
                        caseType = caseRecord.Contains(hexa_Request.Fields.mocd_casetype) ? (int)caseRecord.GetAttributeValue<OptionSetValue>(hexa_Request.Fields.mocd_casetype).Value : 1;
                    }
                    #endregion

                    if (caseType == (int)MOCD.SWP.Common.Resources.CRM.mocd_CaseType.EditCase && !EducationCaseDetails.Any(x => x.ApplyEducationAllowance == true))
                    {
                        foreach (EducationCase item in EducationCaseDetails)
                        {
                            item.IsCompletedFromPortal = true;
                        }
                    }

                }

                var objEducationCaseDetails = new
                {
                    EducationCaseDetails
                };
                return objEducationCaseDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseEducationalAllowance: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

        }

        public static object CheckAllowanceEligibility(IOrganizationService service, string EmiratesId)
        {
            try
            {
                Logger.Info("Inside CheckAllowanceEligibility method");
                Logger.Info("CheckAllowanceEligibility " + JsonConvert.SerializeObject("EmiratesId: " + EmiratesId));

                Guid contactId = Guid.Empty;
                bool eligibleHousing = false;
                bool eligibleEducation = false;
                Guid IdCase = Guid.Empty;

                string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                      <entity name='contact'>
                                        <attribute name='fullname' />
                                        <attribute name='telephone1' />
                                        <attribute name='contactid' />
                                        <order attribute='fullname' descending='false' />
                                        <filter type='and'>
                                          <condition attribute='mocd_emiratesid' operator='eq' value='" + EmiratesId + @"' />
                                        </filter>
                                      </entity>
                                    </fetch>";

                Entity contactRecord = service.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                if (contactRecord != null)
                {
                    contactId = contactRecord.Contains(Contact.Fields.ContactId) ? contactRecord.GetAttributeValue<Guid>(Contact.Fields.ContactId) : Guid.Empty;
                }

                if (contactId != Guid.Empty)
                {
                    KeyValuePair<string, string> eligibleApprovedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);

                    fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                      <entity name='hexa_request'>
                                        <attribute name='hexa_name' />
                                        <attribute name='createdon' />
                                        <attribute name='hexa_requestid' />
                                        <attribute name='hexa_portalcontact' />
                                        <attribute name='hexa_internalstatus' />
                                        <attribute name='hexa_externalstatus' />
                                        <attribute name='mocd_mobilephone' />
                                        <attribute name='mocd_emiratesid' />
                                        <attribute name='mocd_applyforeducationexcellenceallowance' />
                                        <attribute name='mocd_applyforahousingallowance' />
                                        <order attribute='createdon' descending='true' />
                                        <order attribute='hexa_name' descending='true' />
                                        <filter type='and'>
                                          <condition attribute='hexa_portalcontact' operator='eq' uiname='' uitype='contact' value='" + contactId + @"' />
                                          <condition attribute='hexa_internalstatus' operator='eq' uiname='Eligible / Approved' uitype='hexa_processstatustemplate' value='" + eligibleApprovedInternalStatus.Value + @"' />
                                        </filter>
                                      </entity>
                                    </fetch>";

                    Entity record = service.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    if (record != null)
                    {
                        eligibleHousing = record.Contains(hexa_Request.Fields.mocd_ApplyforaHousingAllowance) ? !(record.GetAttributeValue<bool>(hexa_Request.Fields.mocd_ApplyforaHousingAllowance)) : true;
                        IdCase = record.Contains(hexa_Request.Fields.hexa_RequestId) ? record.GetAttributeValue<Guid>(hexa_Request.Fields.hexa_RequestId) : Guid.Empty;

                        List<EducationCase> educationCaseDetails = null;
                        object educationalAllowance = GetCaseEducationalAllowance(IdCase, service);
                        if (educationalAllowance != null)
                        {
                            dynamic educationDetails = educationalAllowance;
                            educationCaseDetails = educationDetails.EducationCaseDetails;
                        }

                        if (educationCaseDetails != null && educationCaseDetails.Count > 0)
                        {
                            if (educationCaseDetails.Any(x => x.ApplyEducationAllowance == true))
                            {
                                eligibleEducation = false;
                            }
                            else
                            {
                                eligibleEducation = true;
                            }

                        }


                        fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                        <entity name='hexa_request'>
                                          <attribute name='hexa_name' />
                                          <attribute name='createdon' />
                                          <attribute name='hexa_requestid' />
                                          <attribute name='mocd_applyforeducationexcellenceallowance' />
                                          <attribute name='mocd_applyforahousingallowance' />
                                          <attribute name='hexa_internalstatus' />
                                          <order attribute='createdon' descending='true' />
                                          <order attribute='hexa_name' descending='true' />
                                          <filter type='and'>
                                            <condition attribute='hexa_parentrequest' operator='eq' uiname='74235' uitype='hexa_request'   value='" + IdCase + @"' />
                                          </filter>
                                        </entity>
                                      </fetch>";

                        Entity childRecord = service.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                        if (childRecord != null)
                        {
                            KeyValuePair<string, string> chidEligibleApprovedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);
                            KeyValuePair<string, string> childInelegibileRejectedInternalStatus = HexaConfiguration.FetchDataFromConfiguration("InelegibileRejected_InternalStatus", service); Guid childInternalStatus = childRecord.Contains(hexa_Request.Fields.hexa_InternalStatus) ? childRecord.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;

                            if (childInternalStatus.ToString().ToLower() != chidEligibleApprovedInternalStatus.Value.ToLower() || childInternalStatus.ToString().ToLower() != childInelegibileRejectedInternalStatus.Value.ToLower())
                            {
                                eligibleHousing = false;
                                eligibleEducation = false;
                                IdCase = Guid.Empty;
                            }
                        }
                    }
                }
                var objEducationCaseDetails = new
                {
                    eligibleHousing,
                    eligibleEducation,
                    IdCase

                };
                return objEducationCaseDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from CheckAllowanceEligibility: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static object CheckInflationAllowanceEligibility(IOrganizationService service, Guid ContactId)
        {
            try
            {
                Logger.Info("Inside CheckInflationAllowanceEligibility method");
                Logger.Info("CheckInflationAllowanceEligibility " + JsonConvert.SerializeObject("ContactId: " + ContactId));

                bool eligibleInflation = true;
                Guid IdCase = Guid.Empty;
                string CaseNumber = string.Empty;

                string fetchXML = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='hexa_request'>
    <attribute name='hexa_name' />
    <attribute name='createdon' />
    <attribute name='hexa_requestid' />
    <attribute name='hexa_portalcontact' />
    <attribute name='ownerid' />
    <attribute name='hexa_internalstatus' />
    <attribute name='hexa_externalstatus' />
    <attribute name='mocd_mobilephone' />
    <attribute name='mocd_emiratesid' />
    <attribute name='emailaddress' />
    <order attribute='createdon' descending='true' />
    <order attribute='hexa_name' descending='true' />
    <filter type='and'>
      <condition attribute='hexa_portalcontact' operator='eq' uiname='' uitype='contact' value='" + ContactId + @"' />
      <condition attribute='hexa_internalstatus' operator='not-in'>
        <value uiname='Not Eligible / Rejected - غير مؤهل / مرفوض' uitype='hexa_processstatustemplate'>{E3E86C3A-A66C-ED11-81AC-0022480DA504}</value>
        <value uiname='Stopped' uitype='hexa_processstatustemplate'>{B7302590-EBE8-ED11-8848-6045BD6972E9}</value>
        <value uiname='Stopped / تم الايقاف' uitype='hexa_processstatustemplate'>{99AB360B-AAC8-ED11-B597-6045BD697183}</value>
        <value uiname='Stopped - COB / متوقفة - تغيير في المستفيد' uitype='hexa_processstatustemplate'>{59C85B17-AAC8-ED11-B597-6045BD697183}</value>
      </condition>
      <condition attribute='mocd_inflationtype' operator='in'>
        <value>662410000</value>
        <value>662410001</value>
      </condition>
      <filter type='or'>
        <condition attribute='mocd_casetype' operator='eq' value='8' />
        <filter type='and'>
          <condition attribute='mocd_applyforinflationallowance' operator='eq' value='1' />
          <condition attribute='mocd_casetype' operator='ne' value='8' />
        </filter>
      </filter>
    </filter>
  </entity>
</fetch>";
                EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchXML));

                if (records.Entities != null && records.Entities.Count > 0)
                {
                    Entity entity = records.Entities.FirstOrDefault();
                    if (entity != null)
                    {
                        IdCase = entity.Id;
                        CaseNumber = entity.Contains("hexa_name") ? entity.GetAttributeValue<string>("hexa_name") : String.Empty;
                    }
                    eligibleInflation = false;
                }

                var objEducationCaseDetails = new
                {
                    eligibleInflation,
                    IdCase,
                    CaseNumber

                };
                return objEducationCaseDetails;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from CheckInflationAllowanceEligibility: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public static async Task<object> GetCaseRefundInstallments(Guid caseId, IOrganizationService service)
        {
            try
            {
                List<CaseRefundInstallments> listCaseRefundInstallments = new List<CaseRefundInstallments>();
                decimal? CaseTotalRefundAmount = 0;
                decimal? CaseAccumilatedAmount = null;
                decimal? CasePendingAmount = null;
                decimal? CaseReClaimAmount = null;
                Guid CurrentInstallment = Guid.Empty;

                #region Get RefundPaymentHeader
                QueryExpression caseRefundPaymentHeaderQuery = new QueryExpression(mocd_refundpaymentheader.EntityLogicalName)
                {
                    ColumnSet = new ColumnSet(mocd_refundpaymentheader.Fields.Id, mocd_refundpaymentheader.Fields.mocd_totalpendingamount, mocd_refundpaymentheader.Fields.mocd_paidrefundamount, mocd_refundpaymentheader.Fields.mocd_installmentrate, mocd_refundpaymentheader.Fields.mocd_TotalRefundAmount, mocd_refundpaymentheader.Fields.mocd_accumulatedamount, mocd_refundpaymentheader.Fields.mocd_financialstatus),
                    Criteria = new FilterExpression(LogicalOperator.And)
                };
                caseRefundPaymentHeaderQuery.Criteria.AddCondition(mocd_refundpaymentheader.Fields.mocd_case, ConditionOperator.Equal, caseId);
                List<Entity> caseRefundPaymentHeaders = service.RetrieveMultiple(caseRefundPaymentHeaderQuery).Entities.ToList();
                #endregion
                if (caseRefundPaymentHeaders.Count > 0)
                {
                    mocd_refundpaymentheader refundPaymentHeader = caseRefundPaymentHeaders.FirstOrDefault().ToEntity<mocd_refundpaymentheader>();
                    CaseTotalRefundAmount = refundPaymentHeader.mocd_TotalRefundAmount != null ? refundPaymentHeader.mocd_TotalRefundAmount.Value : 0;
                    CaseAccumilatedAmount = refundPaymentHeader.mocd_accumulatedamount != null ? refundPaymentHeader.mocd_accumulatedamount.Value : 0;
                    CasePendingAmount = refundPaymentHeader.mocd_totalpendingamount != null ? refundPaymentHeader.mocd_totalpendingamount.Value : 0;
                    CaseReClaimAmount = refundPaymentHeader.mocd_paidrefundamount != null ? refundPaymentHeader.mocd_paidrefundamount.Value : 0;
                    Guid refundPaymentHeaderGuid = refundPaymentHeader.Id;
                    CurrentInstallment = refundPaymentHeader.mocd_installmentrate != null ? refundPaymentHeader.mocd_installmentrate.Id : Guid.Empty;

                    if (refundPaymentHeader.mocd_financialstatus == mocd_FinancialTransactionStatus.StopAllowance)
                    {
                        EntityCollection Installments = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseRefundInstallmentsXML(refundPaymentHeaderGuid)));
                        if (Installments.Entities != null && Installments.Entities.Count > 0)
                        {
                            foreach (mocd_refundpayment RefundInstallment in Installments.Entities)
                            {
                                Guid refundPaymentId = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_refundpaymentId) ? RefundInstallment.GetAttributeValue<Guid>(mocd_refundpayment.Fields.mocd_refundpaymentId) : Guid.Empty;
                                int? status = RefundInstallment.Contains(mocd_refundpayment.Fields.StatusCode) ? (int?)RefundInstallment.GetAttributeValue<OptionSetValue>(mocd_refundpayment.Fields.StatusCode).Value : null;
                                decimal? Amount = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_pendingamount) ? (decimal?)RefundInstallment.GetAttributeValue<Money>(mocd_refundpayment.Fields.mocd_pendingamount).Value : null;
                                string TransactionID = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_transactionid) ? RefundInstallment.GetAttributeValue<string>(mocd_refundpayment.Fields.mocd_transactionid) : string.Empty;

                                if (!string.IsNullOrEmpty(TransactionID) && status.HasValue && status == (int?)mocd_refundpayment_StatusCode.PendingPayment)
                                {
                                    InquiryPaymentRefundResponse objInquiryPaymentRefundResponse = await RefundService.InquiryPayment(service, TransactionID);
                                    if (objInquiryPaymentRefundResponse != null && !string.IsNullOrEmpty(objInquiryPaymentRefundResponse.status))
                                    {
                                        if (status.HasValue && status.Value == (int?)mocd_refundpayment_StatusCode.PendingPayment)
                                        {
                                            if (objInquiryPaymentRefundResponse.status.Trim().ToUpper() == "CAPTURED")
                                            {
                                                mocd_refundpayment request = new mocd_refundpayment()
                                                {
                                                    Id = refundPaymentId,
                                                    StateCode = mocd_refundpaymentState.Inactive,
                                                    StatusCode = mocd_refundpayment_StatusCode.PaymentCompleted_Inactive,
                                                    mocd_pendingamount = new Money(0),
                                                    mocd_reclaimedamount = new Money(Amount.Value),
                                                    mocd_transactiondate = DateTime.Now
                                                };
                                                service.Update(request);
                                            }
                                        }
                                    }
                                }
                            }

                            #region Updatting Amounts on Case Level
                            Decimal totalRemainingAmount = 0;
                            Decimal totalClaimedAmount = 0;

                            EntityCollection CaseInstallments = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseRefundInstallmentsXML(refundPaymentHeaderGuid)));
                            if (CaseInstallments.Entities != null && CaseInstallments.Entities.Count > 0)
                            {
                                foreach (mocd_refundpayment refundRecord in CaseInstallments.Entities)
                                {
                                    if (refundRecord.StatusCode == mocd_refundpayment_StatusCode.PaymentCompleted_Inactive)
                                    {
                                        totalClaimedAmount += refundRecord.mocd_reclaimedamount != null ? refundRecord.mocd_reclaimedamount.Value : 0;
                                    }
                                    else
                                    {
                                        totalRemainingAmount += refundRecord.mocd_pendingamount != null ? refundRecord.mocd_pendingamount.Value : 0;
                                    }
                                }
                                service.Update(new mocd_refundpaymentheader
                                {
                                    Id = refundPaymentHeaderGuid,
                                    mocd_paidrefundamount = new Money(totalClaimedAmount),
                                    mocd_totalpendingamount = new Money(totalRemainingAmount)
                                });

                                if (totalRemainingAmount == 0)
                                {
                                    service.Update(new mocd_refundpaymentheader
                                    {
                                        Id = refundPaymentHeaderGuid,
                                        StatusCode = mocd_refundpaymentheader_StatusCode.Refunded_Inactive,
                                        StateCode = mocd_refundpaymentheaderState.Inactive,
                                    });

                                    KeyValuePair<string, string> eligableApprovedPendingRefund = HexaConfiguration.FetchDataFromConfiguration("EligableApprovedPendingRefund_InternalStatus", service);
                                    KeyValuePair<string, string> notEligableApprovedPendingRefund = HexaConfiguration.FetchDataFromConfiguration("NotEligablePendingRefund_InternalStatus", service);
                                    KeyValuePair<string, string> eligableApprovedRefunded = HexaConfiguration.FetchDataFromConfiguration("EligableApprovedRefunded_InternalStatus", service);
                                    KeyValuePair<string, string> notEligableApprovedRefunded = HexaConfiguration.FetchDataFromConfiguration("NotEligableRefunded_InternalStatus", service);

                                    Guid caseInternalStatus = Guid.Empty;
                                    EntityCollection CaseRecords = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseByCaseIdXML(caseId)));
                                    if (CaseRecords.Entities != null && CaseRecords.Entities.Count > 0)
                                    {
                                        Entity CaseEntity = CaseRecords.Entities.FirstOrDefault();
                                        caseInternalStatus = CaseEntity.Contains(hexa_Request.Fields.hexa_InternalStatus) ? CaseEntity.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_InternalStatus).Id : Guid.Empty;
                                    }

                                    if (caseInternalStatus == Guid.Parse(eligableApprovedPendingRefund.Value))
                                    {
                                        service.Update(new hexa_Request
                                        {
                                            Id = caseId,
                                            hexa_InternalStatus = new EntityReference(hexa_ProcessStatusTemplate.EntityLogicalName, Guid.Parse(eligableApprovedRefunded.Value)),
                                            hexa_ExternalStatus = new EntityReference(hexa_ProcessStatusTemplate.EntityLogicalName, Guid.Parse(eligableApprovedRefunded.Value)),
                                        });
                                    }
                                    else if (caseInternalStatus == Guid.Parse(notEligableApprovedPendingRefund.Value))
                                    {
                                        service.Update(new hexa_Request
                                        {
                                            Id = caseId,
                                            hexa_InternalStatus = new EntityReference(hexa_ProcessStatusTemplate.EntityLogicalName, Guid.Parse(notEligableApprovedRefunded.Value)),
                                            hexa_ExternalStatus = new EntityReference(hexa_ProcessStatusTemplate.EntityLogicalName, Guid.Parse(notEligableApprovedRefunded.Value)),
                                        });
                                    }
                                }
                            }
                            #endregion


                            EntityCollection refundInstallmentsRecords = service.RetrieveMultiple(new FetchExpression(FetchXML.GetCaseRefundInstallmentsXML(refundPaymentHeaderGuid)));

                            foreach (mocd_refundpayment RefundInstallment in refundInstallmentsRecords.Entities)
                            {
                                CaseRefundInstallments objRefundInstallments = new CaseRefundInstallments();

                                objRefundInstallments.Id = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_refundpaymentId) ? RefundInstallment.GetAttributeValue<Guid>(mocd_refundpayment.Fields.mocd_refundpaymentId) : Guid.Empty;
                                objRefundInstallments.Amount = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_pendingamount) ? (decimal?)RefundInstallment.GetAttributeValue<Money>(mocd_refundpayment.Fields.mocd_pendingamount).Value : null;
                                objRefundInstallments.reClaimAmount = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_reclaimedamount) ? (decimal?)RefundInstallment.GetAttributeValue<Money>(mocd_refundpayment.Fields.mocd_reclaimedamount).Value : null;
                                objRefundInstallments.DueDate = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_paymentduedate) ? RefundInstallment.GetAttributeValue<DateTime?>(mocd_refundpayment.Fields.mocd_paymentduedate) : null;
                                objRefundInstallments.Status = RefundInstallment.Contains(mocd_refundpayment.Fields.StatusCode) ? (int?)RefundInstallment.GetAttributeValue<OptionSetValue>(mocd_refundpayment.Fields.StatusCode).Value : null;
                                objRefundInstallments.TransactionID = RefundInstallment.Contains(mocd_refundpayment.Fields.mocd_transactionid) ? RefundInstallment.GetAttributeValue<string>(mocd_refundpayment.Fields.mocd_transactionid) : string.Empty;

                                listCaseRefundInstallments.Add(objRefundInstallments);
                            }

                            CasePendingAmount = totalRemainingAmount;
                            CaseReClaimAmount = totalClaimedAmount;
                        }
                    }
                }

                var objCaseRefundInstallments = new
                {
                    CurrentInstallment,
                    CaseTotalRefundAmount,
                    CaseAccumilatedAmount,
                    CasePendingAmount,
                    CaseReClaimAmount,
                    listCaseRefundInstallments
                };
                return objCaseRefundInstallments;
            }
            catch (Exception ex)
            {
                Logger.Error("Error returned from GetCaseRefundInstallments: " + ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

        }
    }
}
